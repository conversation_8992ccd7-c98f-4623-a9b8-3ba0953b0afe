import React, { useState, useMemo } from "react";
import { Button, ButtonGroup } from "react-bootstrap";
import { LuDownload, Lu<PERSON>he<PERSON> } from "react-icons/lu";
import { FilterSvg, SortingSvg } from "../../../../utils/SvgUtils";
import { PiUploadBold } from "react-icons/pi";
import { useActionWithFeedback } from "../../../../hooks/useActionWithFeedback";
import { Spinner } from "react-bootstrap";

import ResponseListFilterModal from "./filters/ResponseListFilterModal";
import ResponseListSortModal from "./filters/ResponseListSortModal";
import { ExportOptions, FilterState, SortState } from "../types/chartTypes";
import { ExportService } from "../services/exportService";
import { useChartContext } from "../context/ChartContext";
import { useSurveyData } from "../context/SurveyDataContext";
import { transformApiDataToIndividualResponses } from "../utils/responseDataTransformer";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";

interface ActionButtonsProps {
  onExport?: (options: ExportOptions) => void;
  onResponseListFiltersChange?: (filters: FilterState) => void;
  onResponseListSortChange?: (sort: SortState) => void;
  currentResponseListFilters?: FilterState;
  currentResponseListSort?: SortState;
  showResponseListFilter?: boolean;
}

// Export configuration constants
const EXPORT_CONFIG = {
  PDF: {
    fileName: "survey-response-list.pdf",
    tableSelector: ".k-grid",
  },
  EXCEL: {
    fileName: "survey-qa.xlsx",
  },
} as const;

const ActionButtons: React.FC<ActionButtonsProps> = ({
  onExport,
  onResponseListFiltersChange,
  onResponseListSortChange,
  currentResponseListFilters = {},
  currentResponseListSort = { field: "submittedAt", direction: "desc" },
  showResponseListFilter = true,
}) => {
  const [showResponseListFilterModal, setShowResponseListFilterModal] =
    useState(false);
  const [showResponseListSortModal, setShowResponseListSortModal] =
    useState(false);

  // Context hooks
  useChartContext();
  const { tableData } = useSurveyData();

  // Extract question columns for better export
  const questionColumns = useMemo(() => {
    return tableData
      .filter(
        (q: { questionText?: string; questionId?: string }) =>
          q.questionText && q.questionId
      )
      .map(
        (q: {
          questionId: string;
          questionText: string;
          responseType: string;
        }) => ({
          questionId: q.questionId,
          questionText: q.questionText,
          responseType: q.responseType,
        })
      );
  }, [tableData]);

  // Active filter count calculation
  const activeResponseListFilterCount = useMemo(() => {
    if (
      !currentResponseListFilters ||
      Object.keys(currentResponseListFilters).length === 0
    ) {
      return 0;
    }

    let count = 0;
    Object.entries(currentResponseListFilters).forEach(([key, filterValue]) => {
      if (Array.isArray(filterValue) && filterValue.length > 0) {
        count++;
      } else if (
        filterValue &&
        typeof filterValue === "string" &&
        filterValue.trim() !== ""
      ) {
        count++;
      } else if (
        filterValue &&
        typeof filterValue === "object" &&
        filterValue !== null
      ) {
        const objValue = filterValue as Record<string, unknown>;
        if (key === "dateRange") {
          if (objValue.start || objValue.end) count++;
        } else if (key === "score") {
          if (
            (objValue.min !== null && objValue.min !== undefined) ||
            (objValue.max !== null && objValue.max !== undefined)
          ) {
            count++;
          }
        } else if (Object.keys(objValue).length > 0) {
          count++;
        }
      }
    });
    return count;
  }, [currentResponseListFilters]);

  // Data transformation for exports
  const transformedResponses = useMemo(() => {
    const individualResponses =
      transformApiDataToIndividualResponses(tableData);

    if (individualResponses.length === 0) return [];

    return individualResponses.map((response) => ({
      responseId: response.responseId,
      submittedAt: response.submittedAt,
      respondentInfo: response.respondentInfo,
      property: response.property,
      department: response.department,
      status: response.status,
      score: response.score,
      answers: Object.entries(response.answers).map(([questionId, answer]) => {
        const questionInfo = questionColumns.find(
          (q) => q.questionId === questionId
        );
        return {
          questionId: questionId,
          questionText: questionInfo?.questionText || `Question ${questionId}`,
          answerText: answer as string,
          answerValue: answer,
          questionType: questionInfo?.responseType || "text",
        };
      }),
    }));
  }, [tableData, questionColumns]);

  // PDF Export handler
  const downloadPDF = async () => {
    const contentElement = document.getElementById("pdf-content");

    if (!contentElement) {
      console.error("Content element not found");
      return;
    }

    try {
      const isDarkMode =
        document.documentElement.classList.contains("dark-mode") ||
        document.body.classList.contains("dark-mode") ||
        window.matchMedia("(prefers-color-scheme: dark)").matches;

      // Create a clean version for PDF
      const clonedElement = contentElement.cloneNode(true) as HTMLElement;

      // Create temporary container
      const tempContainer = document.createElement("div");
      tempContainer.style.position = "absolute";
      tempContainer.style.left = "-9999px";
      tempContainer.style.top = "-9999px";
      tempContainer.style.width = "800px";
      tempContainer.style.backgroundColor = isDarkMode ? "#1a1a1a" : "#ffffff";
      tempContainer.style.color = isDarkMode ? "#ffffff" : "#000000";
      tempContainer.style.padding = "20px";
      tempContainer.style.fontFamily = "Arial, sans-serif";

      // Clean up the cloned content
      cleanupForPDF(clonedElement, isDarkMode);
      tempContainer.appendChild(clonedElement);
      document.body.appendChild(tempContainer);

      // Capture with html2canvas
      const canvas = await html2canvas(tempContainer, {
        scale: 1.5,
        backgroundColor: isDarkMode ? "#1a1a1a" : "#ffffff",
        useCORS: true,
        logging: false,
      });

      // Create PDF
      const pdf = new jsPDF("p", "mm", "a4");
      const imgWidth = 210;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      pdf.addImage(
        canvas.toDataURL("image/png"),
        "PNG",
        0,
        0,
        imgWidth,
        imgHeight
      );

      // If content is too long, add pages
      let remainingHeight = imgHeight - 297;
      let yOffset = -297;

      while (remainingHeight > 0) {
        pdf.addPage();
        pdf.addImage(
          canvas.toDataURL("image/png"),
          "PNG",
          0,
          yOffset,
          imgWidth,
          imgHeight
        );
        remainingHeight -= 297;
        yOffset -= 297;
      }

      pdf.save(`survey-report-${Date.now()}.pdf`);

      // Clean up
      document.body.removeChild(tempContainer);
    } catch (error) {
      console.error("Error generating PDF:", error);
      throw error;
    }
  };

  const cleanupForPDF = (element: HTMLElement, isDarkMode: boolean) => {
    // Force styles for better PDF rendering
    element.style.backgroundColor = isDarkMode ? "#1a1a1a" : "#ffffff";
    element.style.color = isDarkMode ? "#ffffff" : "#000000";

    // Fix all tables
    const tables = element.querySelectorAll("table");
    tables.forEach((table) => {
      const tableEl = table as HTMLElement;
      tableEl.style.width = "100%";
      tableEl.style.borderCollapse = "collapse";
      tableEl.style.backgroundColor = isDarkMode ? "#2d2d2d" : "#ffffff";
      tableEl.style.color = isDarkMode ? "#ffffff" : "#000000";
      tableEl.style.border = `1px solid ${isDarkMode ? "#404040" : "#dee2e6"}`;
    });

    // Fix all table cells
    const cells = element.querySelectorAll("td, th");
    cells.forEach((cell) => {
      const cellEl = cell as HTMLElement;
      cellEl.style.padding = "8px";
      cellEl.style.border = `1px solid ${isDarkMode ? "#404040" : "#dee2e6"}`;
      cellEl.style.backgroundColor = isDarkMode ? "#2d2d2d" : "#ffffff";
      cellEl.style.color = isDarkMode ? "#ffffff" : "#000000";
      cellEl.style.fontSize = "12px";
    });

    // Replace icons with text
    const icons = element.querySelectorAll("svg, .fa, .fas, .far, .fab");
    icons.forEach((icon) => {
      const parent = icon.parentElement;
      if (parent) {
        if (
          icon.classList.contains("thumbs-up") ||
          parent.innerHTML.includes("👍")
        ) {
          icon.outerHTML = '<span style="font-size: 14px;">👍</span>';
        } else if (
          icon.classList.contains("thumbs-down") ||
          parent.innerHTML.includes("👎")
        ) {
          icon.outerHTML = '<span style="font-size: 14px;">👎</span>';
        } else {
          icon.remove();
        }
      }
    });

    // Remove unnecessary elements
    const elementsToRemove = element.querySelectorAll(
      ".skip-pdf, .btn, .dropdown, .modal"
    );
    elementsToRemove.forEach((el) => el.remove());
  };

  // Excel Export handler
  const handleExcelExport = async () => {
    if (transformedResponses.length === 0) {
      console.warn("No response data available for Excel export");
      return;
    }

    try {
      await ExportService.exportQAToExcel(transformedResponses, {
        format: "excel",
        includeCharts: false,
        includeRawData: true,
        fileName: EXPORT_CONFIG.EXCEL.fileName,
      });

      if (onExport) {
        onExport({
          format: "excel",
          includeCharts: false,
          includeRawData: true,
          fileName: EXPORT_CONFIG.EXCEL.fileName,
        });
      }
    } catch (error) {
      console.error("Error generating Excel file:", error);
    }
  };

  // PDF export with feedback
  const { status: pdfStatus, executeAction: executePdfDownload } =
    useActionWithFeedback(downloadPDF);

  // Render download button content based on status
  const renderDownloadButtonContent = () => {
    switch (pdfStatus) {
      case "processing":
        return (
          <Spinner
            animation="border"
            size="sm"
            role="status"
            aria-hidden="true"
          />
        );
      case "success":
        return <LuCheck size={18} />;
      default:
        return <LuDownload size={18} />;
    }
  };

  return (
    <div className="d-flex gap-2 justify-content-end">
      <ButtonGroup>
        {/* Sort Button */}
        {showResponseListFilter && (
          <div
            className="user-image d-flex align-items-center justify-content-center text-center mt-3 ms-3 cursor-pointer"
            onClick={() => setShowResponseListSortModal(true)}
            title="Sort Responses"
          >
            <SortingSvg width="17" height="17" className="svgicon" />
          </div>
        )}

        {/* Filter Button */}
        {showResponseListFilter && (
          <div
            className="user-image d-flex align-items-center justify-content-center text-center mt-3 ms-3 cursor-pointer position-relative"
            onClick={() => setShowResponseListFilterModal(true)}
            title="Filter Responses"
          >
            <FilterSvg width="17" height="17" className="svgicon" />
            {activeResponseListFilterCount > 0 && (
              <span
                className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                style={{ fontSize: "0.6rem" }}
              >
                {activeResponseListFilterCount}
              </span>
            )}
          </div>
        )}

        {/* PDF Download Button */}
        <div
          className="user-image d-flex align-items-center justify-content-center text-center mt-3 ms-3 cursor-pointer"
          onClick={executePdfDownload}
          title="Download as PDF"
        >
          {renderDownloadButtonContent()}
        </div>
      </ButtonGroup>

      {/* Excel Export Button */}
      <Button
        variant="outline-light"
        size="sm"
        className="border border-white py-1 mt-3"
        onClick={handleExcelExport}
      >
        <PiUploadBold className="btn-icon-custom" />
        Export as Excel
      </Button>

      {/* Filter Modal */}
      <ResponseListFilterModal
        isOpen={showResponseListFilterModal}
        onClose={() => setShowResponseListFilterModal(false)}
        onApply={() => {}}
        currentFilters={currentResponseListFilters}
        onFiltersChange={onResponseListFiltersChange}
        filterType="responseList"
      />

      {/* Sort Modal */}
      <ResponseListSortModal
        isOpen={showResponseListSortModal}
        onClose={() => setShowResponseListSortModal(false)}
        onApply={() => {}}
        currentSort={currentResponseListSort}
        onSortChange={onResponseListSortChange}
        filterType="responseList"
      />
    </div>
  );
};

export default ActionButtons;
