import React, { useState, useEffect } from "react";
import { Card, Alert } from "react-bootstrap";
import RxReactSelect from "../../../../component/RxReactSelect";
import { Loader, NoData } from "../../../../component";
import QuestionResponseViewer from "../../components/addQuestions/QuestionResponseViewer";
import useSurveyUtil from "../../helper/useDetectSurvayType";
import {
  useGetSurveyResponseUserListMutation,
  useGetSurveyResponseUserByUserIdMutation,
} from "../../../../apis/survaysAPI";
import {
  ISurveyResponseUserList,
  SrveyUserResponseDetails,
  SurveyQuestionResponseDtoList,
} from "../../../../apis/type";
import { Form, Formik } from "formik";

interface UserOption {
  label: string;
  value: string;
}

const ViewAllUserResponses = () => {
  const { surveyId } = useSurveyUtil();

  // API hooks
  const [getSurveyResponseUserList, { isLoading: isLoadingUsers }] =
    useGetSurveyResponseUserListMutation();
  const [getSurveyResponseUserByUserId, { isLoading: isLoadingUserResponse }] =
    useGetSurveyResponseUserByUserIdMutation();

  // State management
  const [userList, setUserList] = useState<ISurveyResponseUserList[]>([]);
  const [selectedUser, setSelectedUser] = useState<UserOption | null>(null);
  const [userResponseDetails, setUserResponseDetails] =
    useState<SrveyUserResponseDetails | null>(null);
  const [error, setError] = useState<string>("");

  // Load user list when component mounts
  useEffect(() => {
    if (surveyId) {
      loadUserList();
    }
  }, [surveyId]);

  const loadUserList = async () => {
    try {
      setError("");
      const response = await getSurveyResponseUserList({ surveyId }).unwrap();
      console.log("response: ", response);
      if (response?.data) {
        setUserList(response.data);
      } else {
        setUserList([]);
      }
    } catch (err: any) {
      console.error("Failed to load user list:", err);
      setError("Failed to load user list. Please try again.");
      setUserList([]);
    }
  };

  // Load selected user's response details
  const loadUserResponse = async (responseId: string) => {
    try {
      setError("");
      setUserResponseDetails(null);
      const response = await getSurveyResponseUserByUserId({
        responseId,
      }).unwrap();
      console.log("response: ", response);
      if (response?.data) {
        setUserResponseDetails(response.data);
      }
    } catch (err: any) {
      console.error("Failed to load user response:", err);
      setError("Failed to load user response. Please try again.");
      setUserResponseDetails(null);
    }
  };

  // Handle user selection
  const handleUserChange = (selectedOption: any) => {
    setSelectedUser(selectedOption);
    if (selectedOption) {
      loadUserResponse(selectedOption.value);
    } else {
      setUserResponseDetails(null);
    }
  };

  // Convert user list to dropdown options
  const userOptions: UserOption[] = userList.map((user) => ({
    label: user.userName,
    value: user.responseId,
  }));

  // Show loading state
  if (isLoadingUsers) {
    return <Loader />;
  }

  return (
    <div className="view-all-user-responses">
      {isLoadingUserResponse && <Loader />}
      {/* User Selection Dropdown */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div className="">
          {userResponseDetails && !isLoadingUserResponse && (
            <>
              <small className="text-muted pb-2">Response Submitted At</small>
              <div className="fw-medium">
                {new Date(userResponseDetails.submittedDate).toLocaleString()}
              </div>
            </>
          )}
        </div>
        <div className="">
          <label className="mb-0">Select User</label>
          <RxReactSelect
            options={userOptions}
            value={selectedUser}
            onChange={handleUserChange}
            placeholder="Select User..."
          />
        </div>
      </div>

      {/* No users found */}
      {userList.length === 0 && !isLoadingUsers && (
        <Card className="p-4 rounded-3 mb-4 custom-card">
          <NoData message="No user responses found for this survey." />
        </Card>
      )}

      {/* User Response Details */}
      <>
        {userResponseDetails && !isLoadingUserResponse && (
          <div className="user-response-details">
            {/* <div className="mb-4 p-3 bg-light rounded">
              <h6 className="mb-2">Response Details</h6>
              <div className="row">
                <div className="col-md-6">
                  <small className="text-muted">Survey:</small>
                  <div className="fw-medium">
                    {userResponseDetails.surveyName}
                  </div>
                </div>
              </div>

              {userResponseDetails.surveyPerformerInfoDto && (
                <div className="mt-3">
                  <h6 className="mb-2">Customer Information</h6>
                  <div className="row">
                    <div className="col-md-4">
                      <small className="text-muted">Name:</small>
                      <div>
                        {userResponseDetails.surveyPerformerInfoDto
                          .customerName || "N/A"}
                      </div>
                    </div>
                    <div className="col-md-4">
                      <small className="text-muted">Email:</small>
                      <div>
                        {userResponseDetails.surveyPerformerInfoDto
                          .customerEmail || "N/A"}
                      </div>
                    </div>
                    <div className="col-md-4">
                      <small className="text-muted">Phone:</small>
                      <div>
                        {userResponseDetails.surveyPerformerInfoDto
                          .customerPhone || "N/A"}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div> */}

            {/* Questions and Responses */}
            <div className="questions-responses">
              {userResponseDetails.surveyQuestionResponseDtoList?.map(
                (questionResponse, index) => (
                  <Card key={questionResponse.id} className="mb-4 custom-card">
                    <Card.Body>
                      <QuestionResponseViewer
                        questionNumber={index + 1}
                        readOnly={true}
                        attachmentViewMode={true}
                        initialAnswer={{
                          answers: questionResponse.answers,
                          comment: questionResponse.comment,
                          surveyId: userResponseDetails.surveyId,
                          surveyQuestionId: questionResponse.id,
                          attachmentDtoList: questionResponse.attachmentDtoList,
                          branchingQuestion: questionResponse.branchingQuestion
                            ? {
                                answers: [],
                                comment:
                                  questionResponse.branchingQuestion.comment ||
                                  "",
                                surveyQuestionId:
                                  questionResponse.branchingQuestion.id || "",
                                attachmentDtoList:
                                  questionResponse.branchingQuestion
                                    .attachmentDtoList || [],
                              }
                            : null,
                        }}
                        question={{
                          id: questionResponse.id,
                          questionText: questionResponse.questionText,
                          responseType: questionResponse.responseType,
                          options: questionResponse.options,
                          rattingIcon: questionResponse.rattingIcon,
                          isRequired: false, // Not needed for display
                          allowComment: !!questionResponse.comment,
                          allowAttachment:
                            questionResponse.attachmentDtoList?.length > 0,
                          attachmentType: [],
                          allowBranching: !!questionResponse.branchingQuestion,
                          branchingQuestion: questionResponse.branchingQuestion,
                          questionOrder: questionResponse.questionOrder,
                          surveyId: userResponseDetails.surveyId,
                          comment: questionResponse.comment || "", // Required property
                          autoTicketGeneration: false, // Required property - not available in response data
                        }}
                      />
                    </Card.Body>
                  </Card>
                )
              )}
            </div>
          </div>
        )}
      </>
      {/* No response selected */}
      {!selectedUser && userList.length > 0 && (
        <div className="text-center py-5 text-muted">
          <p>
            Please select a user from the dropdown above to view their
            responses.
          </p>
        </div>
      )}
    </div>
  );
};

export default ViewAllUserResponses;
