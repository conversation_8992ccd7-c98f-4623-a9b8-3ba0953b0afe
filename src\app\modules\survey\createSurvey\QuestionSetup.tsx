import React, { useEffect, useState } from "react";
import { Form, Formik, FormikHelpers, FormikProps, FormikValues } from "formik";
import { QuestionType, SurveyFormValues } from "./types";
import { GoPlus } from "react-icons/go";
import QuestionResponseViewer from "../components/addQuestions/QuestionResponseViewer";
import Footer from "./Footer";
import SwalMessage from "../../common/SwalMessage";
import AddQuestionModalFormWrapper from "../components/addQuestions/AddQuestionModalFormWrapper";
import { useGetSurveyQuestionMutation } from "../../../apis/survaysAPI";
import { Loader } from "../../../component";
import useSurveyUtil from "../helper/useDetectSurvayType";
import { ISurveyQuestionListItem } from "../../../apis/type";
import { MdOutlineEdit } from "react-icons/md";
import { processApiResponse } from "../../../utils/helper";
import FormLabel from "../../../component/form/FormLabel";

interface QuestionSetupProps {
  handleNext?: () => void;
  handleBack?: () => void;
  viewOnly?: boolean;
  hideAddBtn?: boolean;
  hideFooter?: boolean;
}

const QuestionSetup: React.FC<QuestionSetupProps> = ({
  viewOnly = false,
  handleNext,
  handleBack,
  hideAddBtn = false,
  hideFooter = false,
}) => {
  const { surveyId } = useSurveyUtil();
  const [isAddQuestionModalOpen, setIsAddQuestionModalOpen] = useState(false);
  const [questions, setQuestions] = useState<ISurveyQuestionListItem[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState<
    number | null
  >(null);

  const toggleAddQuestionModal = () => {
    setIsAddQuestionModalOpen((pre) => !pre);
  };

  const handleQuestionAdd = (question: QuestionType) => {
    // if (currentQuestionIndex !== null) {
    //   // Update existing question
    //   const updatedQuestions = [...questions];
    //   updatedQuestions[currentQuestionIndex] = question;
    //   setQuestions(updatedQuestions);
    //   setCurrentQuestionIndex(null);
    // } else {
    //   // Add new question
    //   setQuestions([...questions, question]);
    // }
    // setIsAddQuestionModalOpen(false);
  };

  const handleQuestionEdit = (index: number) => {
    setCurrentQuestionIndex(index);
    setIsAddQuestionModalOpen(true);
  };

  const handleQuestionNext = (question: QuestionType) => {
    // handleQuestionAdd(question);
  };

  const handleGONext = () => {
    if (questions.length > 0) {
      handleNext?.();
    } else {
      SwalMessage(null, "Add al least one Question !", "Ok", "warning", false);
    }
  };

  const [getSurveyQuestion, { isLoading, data: questionList, isError }] =
    useGetSurveyQuestionMutation();

  const handleGetQuestion = () => {
    getSurveyQuestion({
      surveyId,
    })
      .unwrap()
      .then((res) => {
        processApiResponse({
          res,
          onSuccess: (res) => {
            if (res?.data && res?.data?.length > 0) {
              setQuestions(res.data);
            } else {
              setQuestions([]);
            }
          },
        });
        // if (res?.data && res?.data?.length > 0) {
        //   setQuestions(res.data);
        // } else {
        //   setQuestions([]);
        // }
      })
      .catch(() => {
        SwalMessage(null, "Something went wrong", "Ok", "error", false);
      });
  };

  useEffect(() => {
    handleGetQuestion();
  }, [surveyId]);

  return (
    <>
      {isLoading && <Loader />}

      <div
        className={
          questions && questions.length > 0 ? "mt-5" : "custom-card mt-5 mb-5"
        }
        style={{ minHeight: "calc(100vh - 333px)" }}
      >
        {questions?.length === 0 ? (
          <>
            {!hideAddBtn && (
              <div className="text-center py-5">
                <h4 className="text-muted mb-5" style={{ marginTop: 80 }}>
                  Get started by creating your first question!
                </h4>
                <span
                  className="btn rx-btn cursor-pointer d-inline-flex align-items-center justify-content-center"
                  role="button"
                  onClick={toggleAddQuestionModal}
                >
                  <GoPlus className="btn-icon-custom" size={25} /> Add New
                  Questions
                </span>
              </div>
            )}
          </>
        ) : (
          <div className="pt-4">
            {!hideAddBtn && (
              <div className="d-flex justify-content-between align-items-center mb-4">
                <div />
                <span
                  className="btn rx-btn cursor-pointer d-inline-flex align-items-center justify-content-center"
                  role="button"
                  onClick={toggleAddQuestionModal}
                >
                  <GoPlus className="btn-icon-custom" size={25} /> Add New
                  Questions
                </span>
              </div>
            )}
            {questions.length > 0 && (
              <div className="d-flex gap-5">
                <div className="w-100">
                  {questions?.map((question, index) => (
                    <div
                      className="custom-card mb-4 p-5 col-8 w-100"
                      key={index}
                    >
                      <Formik initialValues={{}} onSubmit={() => {}}>
                        <Form>
                          <QuestionResponseViewer
                            question={question}
                            readOnly={viewOnly}
                            questionNumber={index + 1}
                            // onEdit={() => handleQuestionEdit(index)}
                          />
                        </Form>
                      </Formik>
                    </div>
                  ))}
                </div>

                <div
                  className="custom-card mb-4 p-5 col-4"
                  style={{ height: "fit-content" }}
                >
                  {questions?.map((question, index) => (
                    <div
                      className="border rounded p-3 d-flex justify-content-between mb-4"
                      key={`${index}-question-index`}
                    >
                      <FormLabel className="mb-0" required={question?.isRequired}>{index + 1}. {question.questionText}</FormLabel>
                      <span
                        className="cursor-pointer"
                        role="button"
                        onClick={() => handleQuestionEdit(index)}
                      >
                        <MdOutlineEdit className="me-4" size={18} />
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {!hideFooter && (
        <div className="row save-continue-footer">
          <div className="row custom-card d-flex justify-content-end border-0">
            <Footer handleNext={handleGONext} handleBack={handleBack} />
          </div>
        </div>
      )}

      {isAddQuestionModalOpen && (
        <AddQuestionModalFormWrapper
          isOpen={isAddQuestionModalOpen}
          onClose={() => {
            setIsAddQuestionModalOpen(false);
            setCurrentQuestionIndex(null);
            handleGetQuestion();
          }}
          questionNumber={
            currentQuestionIndex?.toString()
              ? currentQuestionIndex + 1
              : questions.length + 1
          }
          initialValues={
            currentQuestionIndex?.toString()
              ? questions[currentQuestionIndex]
              : null
          }
          onSubmit={() => {}}
        />
      )}
    </>
  );
};

export default QuestionSetup;
