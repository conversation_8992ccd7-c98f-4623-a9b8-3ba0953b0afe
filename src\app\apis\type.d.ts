import { RoomChatType } from "../modules/Chat/chatType";

export interface Option {
  label: string;
  value: string;
}
export interface IResponse<T> {
  lotoFormId: string;
  ticketId: string | undefined;
  departmentId: string | undefined;
  propertyId: string | undefined;
  roomChatType: string;
  //TODO : Make above type as seperate type

  success: boolean;
  errormsg: string;
  data: T;
}

export interface IPaginateResponse<T> {
  page: number;
  count: number;
  totalCount: number;
  data: T;
}

export interface SortingColumn {
  sortOrder: number;
  columnName: string;
}

export interface PaginationWithSearch {
  page: number;
  size: number;
  search: string;
}

export interface IPropertyDropdown {
  propertyid: string;
  propertyname: string;
  propertyimage?: string;
}

export interface IPropertyDropdownPayload {
  search?: string;
  userid?: string;
}

export interface ITicketListPayload extends PaginationWithSearch {
  sortingColumns?: SortingColumn[];
  status?: string[];
  tickettype?: string[];
  generalticketstatus?: string[];
  maintenanceticketstatus?: string[];
  safetyticketstatus?: string[];
  pestcontrolticketstatus?: string[];
  priority?: string[];
  severity?: string[];
  members?: string[];
  createdby?: string[];
  loopvendor?: string[];
  resolvedby?: string[];
  origin?: string[];
  attachment?: boolean;
  propertyid?: string[];
  userid?: string;
  fromdate?: string;
  todate?: string;
  archive?: boolean;
  departmentid?: string[];
  companyid?: string[];
  fromdate?: string;
  todate?: string;
  archive?: boolean;
  inspectionId?: string;
}

export interface IUserDropDown {
  value: string;
  label: string;
  image: string;
  jobtitle: string;
}

export interface IUserDropDownPayload {
  search?: string;
  propertyid?: string;
  departmentid?: string;
  ticketid?: string;
}

export interface TicketItem {
  ticketid: string;
  ticketnumber: string;
  summary: string;
  assignedto: string;
  assignedtoimage: string;
  createdbyname: string;
  createdbyimage: string;
  createdon: string;
  resolvedon: string;
  priority: string;
  currentstatus: string;
  tickettype: string;
  assignToMember: AssignToMember;
  obdserverMember: ObdserverMember[];
  involvedMember: InvolvedMember[];
  severitylavelid: string;
  severitylavel: string;
  incidenttypeid: string;
  incidenttype: string;
  immediateaction: string;
  propertyname: string;
}

export interface AssignToMember {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  imageUrl: string;
}

export interface ObdserverMember {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  imageUrl: string;
}

export interface InvolvedMember {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  imageUrl: string;
}

export interface IDepartmentPayload {
  page?: number;
  size?: number;
  search: string;
  sortingColumns?: SortingColumn[];
  propertyId?: string;
  userId?: string;
  isSharedDepartment?: number;
  isCompanyDepartment?: number;
  status?: string;
}

export interface SortingColumn {
  sortOrder: number;
  columnName: string;
}

export interface IDepartmentList {
  departmentid: string;
  departmentname: string;
  departmentimage: string;
  departmentheadname: string;
  departmentheadimage: string;
  members: string;
  propertyname: string;
}

export interface ILotoFormListPayload extends PaginationWithSearch {
  sortingColumns?: SortingColumn[];
  propertyIds?: string[];
  departmentIds?: string[];
}

export interface LotoFromList {
  id: string;
  equipmentId: string;
  qrCodeBase64: string;
  propertyId: string;
  propertyName: string;
  lockBoxNumber: string;
  noOfLocks: number;
  totalProcedures: number;
  completedProcedures: number;
  images: Image[];
  lotoformdisplayid: string;
  anyInProgressProcedures: boolean;
  lotoFormId: string;
}

export interface LotoformUser {
  id: string;
  email: string;
  firstname: string;
  lastname: string;
  imageurl: string;
  usertype?: string;
}

export interface IGetUserDetailsByPropertyPayload {
  // search?: string;
  propertyid?: string;
}
export interface IGetUserDetailsByTicketPayload {
  // search?: string;
  ticketid?: string;
}
export interface IGetUserDetailsByDepartmentPayload {
  // search?: string;
  departmentid?: string;
}

export interface UserDetailsList {
  userDetails: UserDetail[];
}

export interface UserDetail {
  id: string;
  email: string;
  firstname: string;
  lastname: string;
  imageurl: string;
  usertype: string;
}

export type GroupMemberType = "USER" | "ADMIN" | "CREATOR";

export interface RoomDetails {
  roomDeleted: boolean;
  userActive: boolean;
  ticketId: string | null;
  ticketNumber: string | null;
  propertyId: string;
  departmentId: string | null;
  lotoFormId: string | null;
  propertyName: string;
  roomChatType: string;
  roomMemberType: string;
  memberDetailDTOList: MemberDetailDtolist[];
  roomCreatedTime: number;
  departmentName: string | null;
  equipmentId: string | null;
  roomName: string;
  roomProfileUrl: string | null;
  inspectionName: string | null;
  inspectionId: string | null;
}

export interface MemberDetailDtolist {
  id: number;
  firstName: string;
  lastName: string;
  emailAddress: string;
  displayName: string;
  userName: string;
  userToken: string;
  profilePicUrl?: string;
  type: GroupMemberType;
  roomId: number;
}

export interface INotificationListPayload {
  page: number;
  size: number;
  propertyIds?: string[] | null;
  departmentIds?: string[] | null;
}
export interface INotificationList {
  title: string;
  body: string;
  createdDate: number;
  read: boolean;
  notificationId: string;
  redirectionId: string;
  propertyName: string;
  propertyId: string;
  type?: string;
  isAcknowledged: number;
  notificationType: number;
  notificationLogId: string;
}

export interface IMarkReadNotificationPayload {
  notificationIds?: string[];
}

export interface IMarkReadNotification {}

export interface IMarkReadAllNotificationPayload {}
export interface IMarkReadAllNotification {}

export interface IDeleteNotificationPayload {
  notificationIds?: string[];
  propertyIds?: string[] | null;
  departmentIds?: string[] | null;
}
export interface IDeleteNotification {}

// export interface IDeleteAllNotificationPayload {}
// export interface IDeleteAllNotification {}

export interface INotificationCountPayload {}
export interface INotificationCount {
  count: number;
}

export interface DepartmentAssignmentPayload {
  userid: string;
  search?: string;
  propertyid?: string;
}

export interface DepartmentAssignmentItem {
  departmentId: string;
  departmentName: string;
  departmentImage: any;
  members: string;
}

export interface IUnreadChatCountPayload {}

export interface IUnreadChatCountResponse {
  count: number;
}

export interface IMentionNotifiationPayload {
  roomId: number;
  userIds: string[];
}

export interface IEquipmentidsforchat {
  id: string;
  equipmentid: string;
  type: string;
  typeFlag: number;
}

export interface IEquipmentidsforchatPayload {
  search: string;
  propertyid: string;
}

type LotoChatOptions = Option & { flag: number };

export interface IMediaSource {
  src: string;
  type?: string;
}
export interface IRoomAttachment {
  sources?: IMediaSource[];
  src?: string | undefined;
  id: string;
  type: string;
  url: string;
}
export interface IRoomAttachmentsResponse extends IRoomAttachment {
  id: string;
}

export interface IMediaSource {
  src: string;
  type?: string;
}
export interface IRoomAttachment {
  sources?: IMediaSource[];
  src?: string | undefined;
  id: string;
  type: string;
  url: string;
}
export interface IRoomAttachmentsResponse extends IRoomAttachment {
  id: string;
}

export interface PaginationWithSearchSorting extends PaginationWithSearch {
  sortingColumns?: SortingColumn[];
}

export type IInspectionTemplateListPayload = PaginationWithSearchSorting;

export interface InspectionManageTemplateItem {
  templateId: string;
  templateName: string;
  propertiesCount: string;
  inspectionQuestionsCount: string;
  priority: string;
  userId: string;
  fullName: string;
  imageUrl: string;
  templateVersion: string;
}

export interface IInspectionListPayload extends PaginationWithSearch {
  inspectionTemplates: string[];
  properties: string[];
  departments: string[];
  status: string[];
  priorities: string[];
  inspectors: string[];
  ticketStatus: string[];
  fromdate: string;
  todate: string;
  sortingColumns?: SortingColumn[];
}

export interface InspectionListItem {
  inspectionId: string;
  inspectionTitle: string;
  inspectionTemplateTitle: string;
  propertyName: string;
  departmentName: string;
  inspectedByName: string;
  inspectedByImageUrl: string;
  inspectionFor: string;
  inspectionTemplatePriority: string;
  inspectionStatus: string;
}
// Attachment used in Area
export interface ITemplateAreaAttachment {
  attachmentUrl: string;
  attachmentType: string;
  attachmentId?: string;
}

// Attachment used in Question
export type ITemplateQuestionAttachment = ITemplateAreaAttachment;

// Question DTO inside Area
export interface ITemplateQuestion {
  questionText: string;
  questionOrder: number;
  inspectionTemplateQuestionAttachmentDto: ITemplateQuestionAttachment[];
  questionId?: string;
}

// Area DTO inside Template
export interface ITemplateArea {
  areaName: string;
  inspectionTemplateAreaAttachmentDto: ITemplateAreaAttachment[];
  inspectionTemplateQuestionRequestDtos: ITemplateQuestion[];
  areaOrder: number;
  areaId?: string;
}

// Grading Option DTO
export interface ITemplateGradingOption {
  gradingOptionName: string;
  gradingOptionPoint: string;
  gradingOptionId?: string;
}

// Loop Vendor Detail
export interface ILoopVendorDetail {
  loopVendorId: string;
  propertyId: string;
  departmentId: string;
  vendorName?: string;
}

// Main Template Payload
export interface IInspectionTemplateCreatePayload {
  inspectionTemplateName: string;
  priority: string;
  propertyIds: string[];
  departmentIds: string[];
  inspectionTemplateAreaRequestDtos: ITemplateArea[];
  inspectionTemplateGradingOptionRequestDtos: ITemplateGradingOption[];
  inspectionTemplateId?: string;
  loopVendorDetails?: ILoopVendorDetail[];
  isEdit?: boolean;
}

export interface IGetInspectionTemplateByIdPayload {
  inspectionTemplateId: string;
  active?: boolean;
}

export interface TempHistory {
  templateId: string;
  templateVersion: string;
}

export interface IGetInspectionTemplateByIdResponse {
  inspectionTemplateId: string;
  inspectionTemplateName: string;
  priority: string;
  masterInspectionTemplateId: string;
  inspectionTemplateStatus: string;
  inspectionTemplateVersion: string;
  propertyIds: string[];
  departmentIds: string[];
  inspectionTemplateAreaResponseDtos: InspectionTemplateAreaResponseDto[];
  inspectionTemplateGradingOptionResponseDtos: any[];
  templateHistory: TempHistory[];
  loopVendorDetails: ILoopVendorDetail[];
}

export interface InspectionTemplateAreaResponseDto {
  areaId: string;
  areaName: string;
  inspectionTemplateId: string;
  inspectionTemplateAreaAttachment: ITemplateAreaAttachment[];
  inspectionTemplateQuestionResponseDtos: InspectionTemplateQuestionResponseDtos[];
  areaOrder: string;
}

export interface InspectionTemplateQuestionResponseDtos {
  questionId: string;
  questionText: string;
  questionOrder: string;
  inspectionTemplateQuestionAttachment: ITemplateAreaAttachment[];
}

export interface User {
  firstName: string;
  lastName: string;
  imageUrl: string;
  jobTitle?: string; // optional in case some users don't have it
  userId: string;
}

export interface Attachment {
  attachmentId: string;
  attachmentType: string;
  attachmentUrl: string;
}

export interface QuestionResponse {
  answerText: string;
  isTicketRequired: boolean;
  inspectionTemplateAnswerAttachment: Attachment[];
  inspectionTemplateQuestionAttachment: Attachment[];
  isAnswered: boolean;
  questionId: string;
  questionOrder: string;
  questionText: string;
  points: string;
  description: string;
  ticketId: string;
}

export interface AreaResponse {
  areaId: string;
  areaName: string;
  areaOrder: string;
  inspectionTemplateAreaAttachment: Attachment[];
  inspectionTemplateId: string;
  inspectionTemplateQuestionResponseDtos: QuestionResponse[];
}

export interface GradingOption {
  gradingOptionId: string;
  gradingOptionName: string;
  gradingOptionPoint: string;
  inspectionTemplateId: string;
}

export interface ScoreData {
  label: string;
  value: string;
  percentage: string;
  points: string;
}

export interface InspectionScoreResponse {
  datalist: ScoreData[];
}

export interface InspectionReportDetails {
  priority: string;
  createdAt: string;
  createdByUser: User;
  createdByUserSignature: string;
  inspectionForDepartmentName: string;
  inspectionForMember: User;
  inspectionId: string;
  inspectionScoreResponseDto: InspectionScoreResponse;
  inspectionTemplateAreaResponseDtos: AreaResponse[];
  inspectionTemplateGradingOptionResponseDtos: GradingOption[];
  inspectionTemplateId: string;
  inspectionTemplateTitle: string;
  inspectionTime: string;
  inspectionTitle: string;
  latitude: string;
  location: string;
  longitude: string;
  totalQuestions: string;
  departmentDetailsResponse: DepartmentDetailsResponse;
  propertyDetailsResponse: PropertyDetailsResponse;
  permissionToViewMembers: Omit<User, "jobTitle">[]; // members without jobTitle
  signatureDateTime: string;
  finalScore: number;
  emailList: string[];
  id?: string;
  inspectionType?: string; // Optional field for inspection type
  templateVersion?: string; // Optional field for template version
  inspectionStatus?: string; // Optional field for inspection status
}

export interface DepartmentDetailsResponse {
  departmentId: string;
  departmentName: string;
  address: string;
  city: string;
  state: string;
  zipcode: string;
  mobileNumber: string;
  departmentImage: string;
}

export interface PropertyDetailsResponse {
  propertyId: string;
  propertyName: string;
  address: string;
  city: string;
  state: string;
  zipcode: string;
  mobileNumber: string;
  propertyImage: string;
}

export interface InspectionReportPayloadById {
  inspectionId: string;
}

export interface LoopVenderListPayload extends PaginationWithSearchSorting {
  page: number;
  size: number;
  search: string;
  departmentid: string;
  propertyid: string;
  status: number;
}

export interface LoopVenderListItem {
  companyVendorId: string;
  vendorViewId: string;
  vendorName: string;
  vendorCity: string;
  vendorState: string;
  vendorPhoneNumber: string;
  vendorstatus: string;
  vendorAccessType: string;
  members: number;
}

export interface InspectionKnowledgeBaseItem {
  id: number;
  url: string;
  name: string;
  displayName: string;
  description: string;
  displayDate: string;
  thumbnail: string;
  attachmentType: string;
  documentStatus: string;
}

export interface InspectionKnowledgeBasePayload
  extends PaginationWithSearchSorting {
  inspectionTemplateId: string;
  attachmentType: string;
}

export interface UploadInspectionKnowledgeBasePayload {
  multipartFile: File | string;
  name: string;
  description: string;
  thumbnail: string;
  attachmentType: string;
  inspectionTemplateId: string;
}

export interface TicketQuestionPayload {
  ticketId: string;
  inspectionId: string;
}

export interface TicketQuestionResponse {
  id: string;
  ticketId: string;
  inspectionId: string;
  questionText: string;
  checked: boolean;
  note: string;
  answer: string;
}

export interface UpdateTicketQuestCheckPayload {
  id: string;
  checked: boolean;
}

// Inspection Link Items Types
export interface LinkWebLinksPayload {
  inspectionId: string;
  title: string;
  url: string;
}

export interface LinkTicketsPayload {
  inspectionId: string;
  linkedTicketids: string[];
}

export interface GetLinkedTicketsPayload {
  inspectionId: string;
  search?: string;
}

export interface DeleteLinkWeblinkPayload {
  weblinkid: string;
}

export interface LinkedTicket {
  ticketId: string;
  ticketNumber: string;
  ticketSummary: string;
  property: string;
  currentStatus: string;
  priority: string;
  createdbyuserid: string;
  createdbyusername: string;
  createdbyimage: string;
  tickettype: string;
  createdon: string;
  assignedusers: {
    value: string;
    label: string;
    image: string;
  }[];
}

export interface WebLinkItem {
  weblinkid: string;
  webtitle: string;
  weburl: string;
}

export interface GetLinkedTicketsResponse {
  linkticketgriddata: LinkedTicket[];
  weblinkgriddata: WebLinkItem[];
}

export interface ISendMailPayload {
  files: Blob | null;
  to: string;
  subject: string;
  contentType: string;
  fileName: string;
}

export type SurveyType = "GLOBAL" | "LOCAL";

export interface ISurveyGeneralSetupPayload {
  surveyName: string;
  surveyImage: string;
  welcomeMessage: string;
  surveyPropertyIds: string[];
  surveyDepartmentIds: string[];
  surveyTarget: string[];
  closingMessage: string;
  redirectUrl: string;
  isDraft: boolean;
  surveyType: SurveyType;
  id?: string;
}

export interface IGetSurveyPayload extends PaginationWithSearchSorting {
  status: boolean;
}

export interface ISurveyTableItem {
  surveyId: string;
  surveyName: string;
  numberOfQuestion: string;
  numberOfResponse: string;
  outsideContact: boolean;
}

export interface QuestionOptions {
  optionText: string;
  optionValue: string;
  isTicketRequired: boolean;
  id?: string;
}

export interface IAddQuestionToSurveyPayload {
  id?: string;
  surveyId?: string;
  questionText: string;
  responseType: string;
  options: QuestionOptions[];
  rattingIcon: string;
  isRequired: boolean;
  allowAttachment: boolean;
  attachmentType: string[];
  allowComment: boolean;
  comment: string;
  autoTicketGeneration: boolean;
  allowBranching: boolean;
  questionOrder: number;
  branchingQuestion: IBranchingQuestionPayload | null;
}

export interface IBranchingQuestionPayload {
  questionText: string;
  responseType: string;
  attachmentType: string[];
  allowAttachment: boolean;

  id?: string;
  surveyId?: string;
  options?: QuestionOptions[];
  rattingIcon?: string;
  isRequired?: boolean;
  allowComment?: boolean;
  comment?: string;
  autoTicketGeneration?: boolean;
  allowBranching?: boolean;
  questionOrder?: number;
}

export type ISurveyQuestionListItem = IAddQuestionToSurveyPayload;
export interface ISurveyById {
  surveyId: string;
}

export interface ISurveyFullDetails {
  id: string;
  surveyName: string;
  surveyImage: string;
  welcomeMessage: string;
  propertyDetailsResponses: PropertyDetail[];
  departmentDetailsResponses: DepartmentDetail[];
  surveyTarget: SurveyTarget[];
  closingMessage: string;
  redirectUrl: string;
  qrCodeBase64: string;
  isDraft: boolean;
  surveyType: string;
  outsideContact: boolean;
  customerName: boolean;
  customerPhone: boolean;
  customerEmail: boolean;
  isCustomerContactPreferenceRequired: boolean;
  customerContactPreference: string;
  isCustomerPreferredContactTimeRequired: boolean;
  customerPreferredContactTime: string;
  customerContactMessage: string;
  surveyQuestionResponseDtos: SurveyQuestionResponseDto[];
}

export interface PropertyDetail {
  propertyId: string;
  propertyName: string;
  address: string;
  city: string;
  state: string;
  zipcode: string;
  mobileNumber: string;
  propertyImage: string;
}

export interface DepartmentDetail {
  departmentId: string;
  departmentName: string;
  address: string;
  city: string;
  state: string;
  zipcode: string;
  mobileNumber: string;
  departmentImage: string;
}

export interface SurveyTarget {
  userId: string;
  jobTitle: string;
  firstName: string;
  lastName: string;
  imageUrl: string;
  companyName: string;
  email: string;
}

export interface SurveyQuestionResponseDto {
  id: string;
  surveyId: string;
  questionText: string;
  responseType: string;
  options: QuestionOptions[];
  rattingIcon: string;
  isRequired: boolean;
  allowAttachment: boolean;
  attachmentType: string[];
  allowComment: boolean;
  autoTicketGeneration: boolean;
  allowBranching: boolean;
  parentId: string;
  questionOrder: number;
  branchingQuestion?: BranchingQuestion;
}

// Survey Reporting Summary API Types
export interface ISurveyReportingSummaryPayload {
  surveyId: string;
}

export interface SurveyResponseAnswer {
  answerId: string;
  answerText: string;
  responseCount: number;
  percentage: number;
  color?: string;
}

export interface SurveyResponseQuestion {
  questionId: string;
  questionText: string;
  questionNumber: number;
  responseType: string;
  totalResponses: number;
  answeredResponses: number;
  averageScore?: number;
  answers: SurveyResponseAnswer[];
  branchingQuestions?: SurveyResponseQuestion[];
}

export interface SurveyReportingSummaryData {
  surveyId: string;
  surveyName: string;
  totalResponses: number;
  questions: SurveyResponseQuestion[];
  properties: PropertyDetail[];
  departments: DepartmentDetail[];
  categories: string[];
  sentiments: Array<{
    label: string;
    value: string;
    emoji: string;
  }>;
}

export type ISurveyReportingSummaryResponse =
  IResponse<SurveyReportingSummaryData>;

export interface BranchingQuestion {
  id: string;
  surveyId: string;
  questionText: string;
  responseType: string;
  options: string[];
  ratting: number;
  rattingIcon: string;
  isRequired: boolean;
  allowAttachment: boolean;
  attachmentType?: string[];
  allowComment?: boolean;
  autoTicketGeneration?: boolean;
  allowBranching?: boolean;
  parentId?: string;
  questionOrder?: number;
  surveyQuestionId?: string;
}

export interface SurveyConfigurationPayload {
  surveyId: string;
  outsideContact: boolean;
  customerName: boolean;
  customerPhone: boolean;
  customerEmail: boolean;
  isCustomerContactPreferenceRequired: boolean;
  customerContactPreference: string;
  isCustomerPreferredContactTimeRequired: boolean;
  customerPreferredContactTime: string;
  customerContactMessage: string;
}

export interface ISubmitSurveyResponsePayload {
  answers: string[];
  comment: string;
  attachmentDtoList: AttachmentDtoList[];
  branchingQuestion: BranchingQuestionSubmit | null;
  surveyId: string;
  surveyQuestionId: string;

  id?: string;
  surveyResponseId?: string;
}

export interface AttachmentDtoList {
  attachmentId: string;
  attachmentUrl: string;
  attachmentType: string;
}

export interface BranchingQuestionSubmit {
  answers: string[];
  comment: string;
  surveyQuestionId: string;
  attachmentDtoList: AttachmentDtoList[];
  surveyResponseId?: string;
  id?: string;
}

export interface ISurveyResponseUserList {
  responseId: string;
  userName: string;
}

export interface SrveyUserResponseDetails {
  responseId: string
  surveyId: string
  surveyName: string
  submittedDate: string
  surveyPerformerInfoDto: SurveyPerformerInfoDto
  surveyQuestionResponseDtoList: SurveyQuestionResponseDtoList[]
}

export interface SurveyPerformerInfoDto {
  customerName: string
  customerPhone: string
  customerEmail: string
  customerContactPreference: string
  customerPreferredContactTime: string
  customerContactMessage: string
}

export interface SurveyQuestionResponseDtoList {
  id: string
  questionText: string
  responseType: string
  options: QuestionOptions[]
  answers: string[]
  comment: string
  attachmentDtoList: AttachmentDtoList[]
  rattingIcon: string
  questionOrder: number
  // branchingQuestion: BranchingQuestion
  branchingQuestion: any
}
