import React, { useMemo, useEffect } from "react";
import { Card, Alert } from "react-bootstrap";
import { useSurveyData } from "../context/SurveyDataContext";
import { transformApiDataToIndividualResponses } from "../utils/responseDataTransformer";
import { useFilterService } from "../hooks/useFilterService";
import { FilterState } from "../types/chartTypes";
import { NoData } from "../../../../component";

interface ViewAllUserResponsesProps {
  surveyId?: string;
  currentFilters?: FilterState;
}

interface CommentResponse {
  responseId: string;
  submittedAt: string;
  respondentInfo?: {
    name?: string;
    email?: string;
    phone?: string;
  };
  comment: string;
  property?: string;
  department?: string;
  status?: string;
  score?: number;
}

interface CommentQuestion {
  questionId: string;
  questionText: string;
  responseType: string;
  totalAnswers: number;
  responses: CommentResponse[];
}

const ViewComment: React.FC<ViewAllUserResponsesProps> = ({
  surveyId,
  currentFilters,
}) => {
  const { tableData, isLoading, error } = useSurveyData();

  // Initialize filter service for comments
  const { filterCommentsData, applyFilterState, hasActiveFilters } =
    useFilterService({
      componentId: `comments-${surveyId}`,
      filterType: "comments",
    });

  // Apply filters when they change
  useEffect(() => {
    if (currentFilters) {
      applyFilterState(currentFilters);
    }
  }, [currentFilters, applyFilterState]);

  // Filter only COMMENT-type questions and transform them to show individual responses
  const commentQuestions = useMemo((): CommentQuestion[] => {
    if (!tableData || tableData.length === 0) {
      return [];
    }

    // Apply filters to table data first
    let filteredTableData = tableData;
    if (hasActiveFilters) {
      const result = filterCommentsData(tableData);
      filteredTableData = result.data;
    }

    // Filter only COMMENT questions
    const commentQuestionsData = filteredTableData.filter(
      (q) => q.responseType === "COMMENT"
    );

    if (commentQuestionsData.length === 0) {
      return [];
    }

    // Transform to individual responses format
    const individualResponses =
      transformApiDataToIndividualResponses(tableData);

    // Map COMMENT questions to their individual responses
    return commentQuestionsData.map((question) => {
      let questionResponses: CommentResponse[] = [];

      // For comment-type questions, use 'answers' array if present
      if (
        question.responseType === "COMMENT" &&
        Array.isArray(question.answers)
      ) {
        questionResponses = question.answers
          .filter((item: any) => item.answer && item.answer !== "No Response")
          .map((item: any, idx: number) => ({
            responseId: `${question.questionId}-${idx}`,
            submittedAt: "", // No timestamp in this structure
            respondentInfo: {}, // No respondent info in this structure
            comment: item.answer,
            property: undefined,
            department: undefined,
            status: undefined,
            score: undefined,
          }));
      } else {
        // Fallback to previous logic for other types or if 'answers' not present
        individualResponses.forEach((response) => {
          const comment = response.answers[question.questionId];
          if (comment && comment !== "No Response") {
            questionResponses.push({
              responseId: response.responseId,
              submittedAt: response.submittedAt,
              respondentInfo: response.respondentInfo,
              comment: comment,
              property: response.property,
              department: response.department,
              status: response.status,
              score: response.score,
            });
          }
        });
      }

      return {
        questionId: question.questionId,
        questionText: question.questionText,
        responseType: question.responseType,
        totalAnswers: question.totalAnswers,
        responses: questionResponses,
      };
    });
  }, [tableData, hasActiveFilters, filterCommentsData]);

  console.log("commentQuestions:>>>>", commentQuestions);

  // Format date to relative time
  const formatRelativeTime = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return "Today";
    } else if (diffInDays === 1) {
      return "Yesterday";
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else if (diffInDays < 30) {
      const weeks = Math.floor(diffInDays / 7);
      return `${weeks} week${weeks > 1 ? "s" : ""} ago`;
    } else {
      const months = Math.floor(diffInDays / 30);
      return `${months} month${months > 1 ? "s" : ""} ago`;
    }
  };

  if (isLoading) {
    return (
      <Card className="p-4 rounded-3 mb-4">
        <div
          className="d-flex justify-content-center align-items-center"
          style={{ minHeight: "200px" }}
        >
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading responses...</span>
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-4 rounded-3 mb-4">
        <Alert variant="danger">
          <Alert.Heading>Error loading responses</Alert.Heading>
          <p>{error}</p>
        </Alert>
      </Card>
    );
  }

  if (commentQuestions.length === 0) {
    return (
      <Card className="p-4 rounded-3 mb-4 custom-card">
        {/* <Alert variant="info">
          <Alert.Heading>
            <i className="fas fa-comments me-2"></i>
            {hasActiveFilters ? "No comment questions match current filters" : "No comment questions found"}
          </Alert.Heading>
          <p className="mb-0">
            {hasActiveFilters 
              ? "No comment-type questions match the current filter criteria. Try adjusting your filters to see more results."
              : "This survey doesn't contain any comment-type questions. Comment responses will appear here once they are available."
            }
          </p>
        </Alert> */}
        <NoData message="No Data Found" />
      </Card>
    );
  }

  return (
    <div>
      {commentQuestions.map((question, questionIndex) => (
        <Card key={question.questionId} className="mb-4 custom-card">
          <Card.Body className="p-4">
            {/* Question Header */}
            <div className="mb-4">
              <h5
                className="mb-2"
                style={{ fontSize: "1.1rem", fontWeight: "600" }}
              >
                {questionIndex + 1}. {question.questionText}
              </h5>
              <p className="mb-0" style={{ fontSize: "0.9rem" }}>
                {question.responses.length} out of {question.totalAnswers}{" "}
                people answered this question
              </p>
            </div>

            {/* Responses List */}
            <div style={{ maxHeight: "400px", overflowY: "auto" }}>
              {question.responses.length > 0 ? (
                question.responses.map((response) => (
                  <div
                    key={response.responseId}
                    className="mb-3 p-3"
                    style={{
                      borderRadius: "8px",
                      border: "1px solid var(--Gray-1)",
                    }}
                  >
                    {/* Response Text */}
                    <div style={{ fontSize: "0.95rem", lineHeight: "1.4" }}>
                      {response.comment}
                    </div>

                    {/* Timestamp */}
                    {/* <div className="text-muted" style={{ fontSize: "0.8rem" }}>
                      {formatRelativeTime(response.submittedAt)}
                    </div> */}
                  </div>
                ))
              ) : (
                <NoData message="No responses yet for this question" />
              )}
            </div>
          </Card.Body>
        </Card>
      ))}
    </div>
  );
};

export default ViewComment;
