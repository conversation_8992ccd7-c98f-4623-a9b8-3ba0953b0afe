import React, { useEffect } from "react";
import { Card } from "react-bootstrap";
import SecondaryTabs from "./SecondaryTabs";
import FilterPanel from "./FilterPanel";
import SummaryCard from "./SummaryCard";
import ChartContainer from "./ChartContainer";
import FullResponseList from "./FullResponseList";
import { ChartContextProvider } from "../context/ChartContext";
import { useSurveyData } from "../context/SurveyDataContext";
import { Loader } from "../../../../component";
import ViewAllUserResponses from "./ViewAllUserResponses";
import ViewComment from "./ViewComment";

interface ReportingContentProps {
  surveyId: string;
  activeSubTab: string;
  onSubTabChange: (tab: string) => void;
}

const ReportingContent: React.FC<ReportingContentProps> = ({
  surveyId,
  activeSubTab,
  onSubTabChange,
}) => {
  const { fetchSurveyData, isLoading, error } = useSurveyData();

  useEffect(() => {
    if (surveyId) {
      console.log(
        "🔄 ReportingContent: Fetching survey data for surveyId:",
        surveyId
      );
      fetchSurveyData(surveyId);
    }
  }, [surveyId, fetchSurveyData]);

  if (isLoading) {
    return <div>{isLoading && <Loader />}</div>;
  }

  return (
    <>
      {/* Add this wrapper */}
      <ChartContextProvider>
        {/* Secondary tabs */}
        <div className="mb-4">
          <SecondaryTabs
            activeTab={activeSubTab}
            onTabChange={onSubTabChange}
          />
        </div>

        {/* Filter panel */}
        <div id="pdf-content" className="mb-4">
          {(activeSubTab === "goose" || activeSubTab === "summary") && (
            <>
              {activeSubTab === "goose" && <FilterPanel />}
              <div className="mb-4">
                <ChartContainer activeTab={activeSubTab} />
              </div>
              {activeSubTab === "goose" && (
                <div className="mb-4">
                  <SummaryCard />
                </div>
              )}
            </>
          )}

          {activeSubTab === "responses" && (
            <div className="mb-4">
              <FullResponseList surveyId={surveyId} activeTab={activeSubTab} />
            </div>
          )}

          {activeSubTab === "viewComments" && (
            <ViewComment surveyId={surveyId} />
          )}
          {activeSubTab === "allUserResponses" && <ViewAllUserResponses />}
        </div>
      </ChartContextProvider>
    </>
  );
};

export default ReportingContent;
