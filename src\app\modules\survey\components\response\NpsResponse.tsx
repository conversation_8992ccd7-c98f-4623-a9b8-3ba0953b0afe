import React, { useState, useEffect } from 'react';

interface NpsResponseProps {
  startNetPromoterScore: number;
  endNetPromoterScore: number;
  value: number;
  onChange: (value: number) => void;
  readOnly?: boolean;
}

const NpsResponse: React.FC<NpsResponseProps> = ({ startNetPromoterScore, endNetPromoterScore, value, onChange, readOnly }) => {
  const [selected, setSelected] = useState(value);

  useEffect(() => {
    setSelected(value);
  }, [value]);

  const handleSelect = (val: number) => {
    setSelected(val);
    onChange(val);
  };

  return (
    <div style={{ display: 'flex', gap: 12 }}>
      {Array.from({ length: endNetPromoterScore - startNetPromoterScore + 1 }, (_, i) => {
        const num = startNetPromoterScore + i;
        const isSelected = selected === num;
        return (
          <div
            key={num}
            onClick={() => readOnly ? null : handleSelect(num)}
            style={{
              width: 36,
              height: 36,
              borderRadius: '50%',
              border: isSelected ? '2px solid #007bff' : '1px solid #ccc',
              background: isSelected ? '#007bff' : '#fff',
              color: isSelected ? '#fff' : '#333',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontWeight: 600,
              fontSize: 18,
              cursor: readOnly ? 'not-allowed' : 'pointer',
              boxShadow: isSelected ? '0 0 8px #007bff33' : 'none',
              transition: 'all 0.2s',
            }}
          >
            {num}
          </div>
        );
      })}
    </div>
  );
};

export default NpsResponse; 