import React from 'react';
import RxReactSelect from '../../../../component/RxReactSelect';

interface MultipleChoiceResponseProps {
  options: { label: string; value: string }[];
  value?: string;
  readOnly?: boolean;
  onChange?: (value: string | number) => void;
}

const MultipleChoiceResponse: React.FC<MultipleChoiceResponseProps> = ({
  options,
  value,
  onChange,
  readOnly = false,
}) => {
  // Find the selected option object based on the value
  const selectedOption = options.find(option => option.value === value) || null;

  return (
    <RxReactSelect
      options={options}
      value={selectedOption}
      isDisabled={readOnly}
      placeholder="Select an option"
      onChange={(selectedOption: any) => {
        if (onChange && selectedOption) {
          onChange(selectedOption.value);
        }
      }}
    />
  );
};

export default MultipleChoiceResponse; 