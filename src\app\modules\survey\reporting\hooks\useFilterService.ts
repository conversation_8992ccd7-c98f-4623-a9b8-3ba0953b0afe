import { useState, useCallback, useMemo } from 'react';
import FilterService, { 
  CombinedFilterState, 
  FilterResult, 
  FilterType,
  FilterConfig 
} from '../services/filterService';
import { FilterState } from '../types/chartTypes';
import { SurveyResponseTableRow } from '../types/chartTypes';
import { TransformedRespondentResponse } from '../utils/responseDataTransformer';

interface UseFilterServiceOptions {
  componentId: string;
  filterType: FilterType;
}

export const useFilterService = ({ componentId, filterType }: UseFilterServiceOptions) => {
  const [currentFilters, setCurrentFilters] = useState<CombinedFilterState>({});
  const filterService = useMemo(() => FilterService.getInstance(), []);

  // Apply filters to chart data
  const filterChartData = useCallback(<T extends { questionId: string; responseType: string }>(
    data: T[]
  ): FilterResult<T> => {
    return filterService.filterChartData(data, currentFilters);
  }, [filterService, currentFilters]);

  // Apply filters to response list data
  const filterResponseListData = useCallback((
    data: TransformedRespondentResponse[]
  ): FilterResult<TransformedRespondentResponse> => {
    return filterService.filterResponseListData(data, currentFilters);
  }, [filterService, currentFilters]);

  // Apply filters to comments data
  const filterCommentsData = useCallback((
    data: SurveyResponseTableRow[]
  ): FilterResult<SurveyResponseTableRow> => {
    return filterService.filterCommentsData(data, currentFilters);
  }, [filterService, currentFilters]);

  // Update filters
  const updateFilters = useCallback((newFilters: CombinedFilterState) => {
    setCurrentFilters(newFilters);
    filterService.setFilters(componentId, newFilters);
  }, [filterService, componentId]);

  // Convert and apply FilterState
  const applyFilterState = useCallback((filterState: FilterState) => {
    const combinedFilters = filterService.convertFilterState(filterState);
    updateFilters(combinedFilters);
  }, [filterService, updateFilters]);

  // Clear all filters
  const clearFilters = useCallback(() => {
    const emptyFilters: CombinedFilterState = {};
    setCurrentFilters(emptyFilters);
    filterService.clearFilters(componentId);
  }, [filterService, componentId]);

  // Get active filter count
  const getActiveFilterCount = useCallback((): number => {
    return filterService.getActiveFilterCount(currentFilters);
  }, [filterService, currentFilters]);

  // Create a new filter
  const createFilter = useCallback((
    id: string,
    name: string,
    field: string,
    operator: FilterConfig['operator'],
    value: unknown
  ): FilterConfig => {
    return filterService.createFilter(id, name, filterType, field, operator, value);
  }, [filterService, filterType]);

  // Validate a filter
  const validateFilter = useCallback((filter: FilterConfig): boolean => {
    return filterService.validateFilter(filter);
  }, [filterService]);

  // Get stored filters for this component
  const getStoredFilters = useCallback((): CombinedFilterState | undefined => {
    return filterService.getFilters(componentId);
  }, [filterService, componentId]);

  return {
    // Current state
    currentFilters,
    activeFilterCount: getActiveFilterCount(),
    
    // Filter methods
    filterChartData,
    filterResponseListData,
    filterCommentsData,
    
    // Filter management
    updateFilters,
    applyFilterState,
    clearFilters,
    createFilter,
    validateFilter,
    getStoredFilters,
    
    // Utility methods
    hasActiveFilters: getActiveFilterCount() > 0
  };
}; 