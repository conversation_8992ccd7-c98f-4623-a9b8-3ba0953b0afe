import React, { useMemo, useState, useEffect } from "react";
import { Card, Alert } from "react-bootstrap";
import { orderBy } from "@progress/kendo-data-query";
import { GridColumn as Column, Grid } from "@progress/kendo-react-grid";
import { Tooltip } from "@progress/kendo-react-tooltip";
import { useSurveyData } from "../context/SurveyDataContext";
import { transformApiDataToIndividualResponses } from "../utils/responseDataTransformer";
import { useFilterService } from "../hooks/useFilterService";
import { FilterState } from "../types/chartTypes";
import {
  emojiOptions,
  QUESTION_TYPE_OPTIONS,
  thumbsOptions,
} from "../../components/addQuestions/util/constant";
import { NoData } from "../../../../component";

interface FullResponseListProps {
  surveyId?: string;
  activeTab?: string;
  currentFilters?: FilterState;
}

// New interfaces for question-based table structure
interface RespondentResponse {
  responseId: string;
  submittedAt: string;
  respondentInfo?: {
    name?: string;
    email?: string;
    phone?: string;
  };
  answers: { [questionId: string]: string };
  property?: string;
  department?: string;
  status?: string;
  score?: number;
}

interface QuestionColumn {
  questionId: string;
  questionText: string;
  responseType: string;
}

interface QuestionTableRow {
  questionId: string;
  questionText: string;
  responseType: string;
  totalAnswers: number;
  answers: Array<{
    answer: string;
    comment: string;
    count: number;
    percentage: number;
  }>;
}

// Kendo Grid cell props interfaces
interface RespondentGridCellProps {
  dataItem: RespondentResponse;
  field?: string;
  columnIndex?: number;
  rowIndex?: number;
}

interface QuestionGridCellProps {
  dataItem: QuestionTableRow;
  field?: string;
  columnIndex?: number;
  rowIndex?: number;
}

const FullResponseList: React.FC<FullResponseListProps> = ({
  surveyId,
  activeTab,
  currentFilters,
}) => {
  // Use the survey data context - data should already be loaded by parent component
  const { tableData, isLoading, error } = useSurveyData();

  // Initialize filter service for response list
  const {
    filterResponseListData,
    applyFilterState,
    hasActiveFilters
  } = useFilterService({
    componentId: `responses-${activeTab}`,
    filterType: 'responses'
  });

  // Apply filters when they change
  useEffect(() => {
    if (currentFilters) {
      applyFilterState(currentFilters);
    }
  }, [currentFilters, applyFilterState]);

  // State to toggle between old and new table layouts
  const [useQuestionBasedLayout] = useState(true); // Default to Question View

  // Transform aggregated API data into individual response records
  const rawIndividualResponsesData = useMemo((): RespondentResponse[] => {
    if (!tableData || tableData.length === 0) {
      return [];
    }
    const transformedData = transformApiDataToIndividualResponses(tableData);
    return transformedData;
  }, [tableData]);

  // Apply filters to individual responses data
  const individualResponsesData = useMemo(() => {
    if (!rawIndividualResponsesData || rawIndividualResponsesData.length === 0) {
      return [];
    }

    if (hasActiveFilters) {
      const result = filterResponseListData(rawIndividualResponsesData);
      return result.data;
    }

    return rawIndividualResponsesData;
  }, [rawIndividualResponsesData, hasActiveFilters, filterResponseListData]);

  // Extract question columns from tableData
  const questionColumns = useMemo((): QuestionColumn[] => {
    return tableData
      .filter(
        (q) =>
          QUESTION_TYPE_OPTIONS.map((opt) => opt.value).includes(
            q.responseType
          ) && q.responseType !== "COMMENT"
      )
      .map((q) => ({
        questionId: q.questionId,
        questionText: q.questionText,
        responseType: q.responseType,
      }));
  }, [tableData]);



  // Memoize grid data to prevent unnecessary re-renders
  const gridData = useMemo(() => {
    return orderBy(tableData, [{ field: "questionText", dir: "asc" }]);
  }, [tableData]);

  // Memoize respondent grid data for new layout
  const respondentGridData = useMemo(() => {
    return orderBy(individualResponsesData, [
      { field: "submittedAt", dir: "desc" },
    ]);
  }, [individualResponsesData]);

  // Cleanup any remaining custom tooltips on component unmount
  useEffect(() => {
    return () => {
      // Clean up any remaining custom tooltips
      const tooltips = document.querySelectorAll(".custom-question-tooltip");
      tooltips.forEach((tooltip) => {
        if (tooltip.parentNode) {
          tooltip.parentNode.removeChild(tooltip);
        }
      });
    };
  }, []);

  const renderQuestionAnswer =
    (questionId: string) => (props: RespondentGridCellProps) => {
      const respondent = props.dataItem;
      const answer = respondent.answers[questionId];

      const renderIcon = () => {
        const emojiMatch = emojiOptions.find((opt) => opt.value === answer);
        if (emojiMatch) {
          return (
            <span title={emojiMatch.label} style={{ fontSize: "1.2rem" }}>
              {emojiMatch.icon}
            </span>
          );
        }

        const thumbMatch = thumbsOptions.find((opt) => opt.value === answer);
        if (thumbMatch) {
          const IconComponent = thumbMatch.icon;
          return (
            <span
              title={thumbMatch.label}
              className="d-inline-flex align-items-center"
            >
              <IconComponent size={20} />
            </span>
          );
        }

        return <span>{answer}</span>;
      };

      return (
        <td className="k-table-td">
          <div className="text-center">
            {answer && answer !== "No Response" ? (
              renderIcon()
            ) : (
              <span className="text-muted fst-italic">
                <i className="fas fa-minus me-1"></i>
                No Response
              </span>
            )}
          </div>
        </td>
      );
    };

  // Custom header renderer for question columns with text wrapping and tooltips
  const CustomHeaderCell = (props: { title: string }) => {
    const questionText = props.title;

    return (
      <div
        className="question-header-wrapper"
        title={questionText}
        style={{
          minWidth: "180px",
          maxWidth: "180px",
          padding: "8px 4px",
          height: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <span
          style={{
            display: "-webkit-box",
            WebkitLineClamp: 2,
            WebkitBoxOrient: "vertical",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "normal",
            wordBreak: "break-word",
            fontWeight: 600,
            lineHeight: 1.3,
            textAlign: "center",
            height: "2.6em",
            width: "100%",
            color: "var(--Rx-title)"
          }}
        >
          {questionText}
        </span>
      </div>
    );
  };

  // Custom cell renderers following SurveyListPage pattern
  const renderQuestion = (props: QuestionGridCellProps) => {
    const content = props.dataItem;
    return (
      <td className="k-table-td">
        <div
          className="fw-medium text-truncate"
          style={{ lineHeight: "1.4" }}
          title={content.questionText}
        >
          {content.questionText}
        </div>
      </td>
    );
  };

  const renderResponseType = (props: QuestionGridCellProps) => {
    const content = props.dataItem;
    const responseType = content.responseType || "UNKNOWN";

    const getResponseTypeBadge = (type: string) => {
      const badgeClasses = {
        SCALE: "bg-primary",
        RATING: "bg-warning text-dark",
        MULTIPLE_CHOICE: "bg-success",
        YES_NO: "bg-info",
        TEXT: "bg-secondary",
      };
      return badgeClasses[type as keyof typeof badgeClasses] || "bg-secondary";
    };

    const displayText = responseType.replace(/_/g, " ");
    const badgeClass = getResponseTypeBadge(responseType);

    return (
      <td className="k-table-td">
        <div className="d-flex justify-content-center">
          <span className={`badge ${badgeClass} text-white`}>
            {displayText}
          </span>
        </div>
      </td>
    );
  };

  const renderTotalAnswers = (props: QuestionGridCellProps) => {
    const content = props.dataItem;
    return (
      <td className="k-table-td">
        <div className="fw-bold text-center fs-6">{content.totalAnswers}</div>
      </td>
    );
  };

  const renderResponseSummary = (props: QuestionGridCellProps) => {
    const content = props.dataItem;
    const answers = content.answers || [];

    if (answers.length === 0) {
      return (
        <td className="k-table-td">
          <div className="text-muted fst-italic d-flex align-items-center">
            <i className="fas fa-info-circle me-1"></i>
            No responses
          </div>
        </td>
      );
    }

    return (
      <td className="k-table-td">
        <div style={{ fontSize: "0.875rem" }}>
          <table
            className="table table-sm table-borderless mb-0"
            style={{ fontSize: "0.8rem" }}
          >
            <thead>
              <tr>
                <th
                  className="p-1 border-bottom"
                  style={{ fontSize: "0.75rem", fontWeight: "600" }}
                >
                  Answer
                </th>
                <th
                  className="p-1 border-bottom text-center"
                  style={{ fontSize: "0.75rem", fontWeight: "600" }}
                >
                  Count
                </th>
                <th
                  className="p-1 border-bottom text-center"
                  style={{ fontSize: "0.75rem", fontWeight: "600" }}
                >
                  %
                </th>
              </tr>
            </thead>
            <tbody>
              {answers.slice(0, 3).map(
                (
                  answer: {
                    answer: string;
                    count: number;
                    percentage: number;
                  },
                  index: number
                ) => {
                  const displayAnswer = answer.answer || "No answer text";
                  const displayCount = answer.count || 0;
                  const displayPercentage = answer.percentage || 0;

                  return (
                    <tr key={index}>
                      <td
                        className="p-1"
                        style={{
                          maxWidth: "120px",
                          whiteSpace: "nowrap",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                        }}
                        title={displayAnswer}
                      >
                        {displayAnswer}
                      </td>
                      <td className="p-1 text-center fw-medium">
                        {displayCount}
                      </td>
                      <td className="p-1 text-center">
                        {displayPercentage === 100
                          ? "100"
                          : displayPercentage.toFixed(1)}
                        %
                      </td>
                    </tr>
                  );
                }
              )}
            </tbody>
          </table>
          {answers.length > 3 && (
            <div className="text-muted small mt-1 text-center border-top pt-1">
              <i className="fas fa-plus-circle me-1"></i>+{answers.length - 3}{" "}
              more response{answers.length - 3 !== 1 ? "s" : ""}
            </div>
          )}
        </div>
      </td>
    );
  };

  // Show error state
  if (error) {
    return (
      <Card className="p-4 rounded-3 mb-4">
        <Alert variant="danger">
          <Alert.Heading>Error loading survey data</Alert.Heading>
          <p>{error}</p>
        </Alert>
      </Card>
    );
  }

  // Show empty state when no data is available
  if (tableData.length === 0 && !isLoading) {
    return (
      <Card className="p-4 rounded-3 mb-4">
        {/* <Alert variant="info">
          <Alert.Heading>
            <i className="fas fa-table me-2"></i>
            No survey data available
          </Alert.Heading>
          <p className="mb-0">
            {activeTab === "responses" && surveyId
              ? "No survey responses found for this survey. Responses will appear here once participants submit their answers."
              : "Please select a survey to view response data."}
          </p>
        </Alert> */}
        <NoData message="No Data Found" />
      </Card>
    );
  }

  // Show filtered empty state
  if (hasActiveFilters && individualResponsesData.length === 0) {
    return (
      <Card className="p-4 rounded-3 mb-4">
        {/* <Alert variant="info">
          <Alert.Heading>
            <i className="fas fa-filter me-2"></i>
            No responses match current filters
          </Alert.Heading>
          <p className="mb-0">
            No survey responses match the current filter criteria. Try adjusting your filters to see more results.
          </p>
        </Alert> */}
        <NoData message="No Data Found" />
      </Card>
    );
  }

  return (
    <div>
      {/* <div className="row mb-7">
        <div className="col-xl-6 col-lg-6 col-sm-6 mt-auto mb-auto">
          <h5 className="mb-0 text-dark">
            <i className="fas fa-table me-2"></i>
            {useQuestionBasedLayout
              ? `Survey Responses (${individualResponsesData.length} ${
                  individualResponsesData.length === 1
                    ? "response"
                    : "responses"
                })`
              : `Survey Questions (${tableData.length} ${
                  tableData.length === 1 ? "question" : "questions"
                })`}
          </h5>
        </div>
        <div className="col-xl-6 col-lg-6 col-sm-6 text-end">
          <div className="d-flex justify-content-end align-items-center gap-3">
            <div className="text-muted small">
              {useQuestionBasedLayout
                ? "Individual respondent answers by question"
                : "Total responses across all questions"}
            </div>
            <Button
              variant="outline-primary"
              size="sm"
              onClick={() => setUseQuestionBasedLayout(!useQuestionBasedLayout)}
            >
              <i
                className={`fas fa-${
                  useQuestionBasedLayout ? "chart-bar" : "table"
                } me-1`}
              ></i>
              {useQuestionBasedLayout ? "Question View" : "Response View"}
            </Button>
          </div>
        </div>
      </div> */}

      {useQuestionBasedLayout ? (
        // Response View - Individual respondent answers
        <div className="card mt-0">
          <div className="card-body p-0" style={{ margin: "0.8px" }}>
            <div style={{ width: "100%", overflowX: "auto" }}>
              <Grid
                data={respondentGridData}
                sortable={true}
                pageable={{
                  buttonCount: 4,
                  pageSizes: [10, 25, 50, "All"],
                  pageSizeValue: 10,
                }}
              >
                {questionColumns.map((question) => (
                  <Column
                    key={question.questionId}
                    field={`answers.${question.questionId}`}
                    width={214}
                    headerCell={(props) => (
                      <CustomHeaderCell
                        {...props}
                        title={question.questionText}
                      />
                    )}
                    cell={renderQuestionAnswer(question.questionId)}
                  />
                ))}
              </Grid>
            </div>
          </div>
        </div>
      ) : (
        // ORIGINAL: Question summary table layout (commented for reference)
        <div className="card mt-0">
          <div className="card-body p-0" style={{ margin: "0.8px" }}>
            <div className="table_div" style={{ width: "100%" }}>
              <Tooltip position="bottom" anchorElement="target">
                <Grid
                  data={gridData}
                  sortable={true}
                  pageable={{
                    buttonCount: 4,
                    pageSizes: [10, 25, 50, "All"],
                    pageSizeValue: 10,
                  }}
                >
                  <Column
                    title="Question"
                    field="questionText"
                    width="300px"
                    cell={(props) => renderQuestion({ ...props })}
                  />
                  <Column
                    title="Response Type"
                    field="responseType"
                    width="140px"
                    cell={(props) => renderResponseType({ ...props })}
                  />
                  <Column
                    title="Total Answers"
                    field="totalAnswers"
                    width="140px"
                    cell={(props) => renderTotalAnswers({ ...props })}
                  />
                  <Column
                    title="Response Summary"
                    field="answers"
                    width="300px"
                    cell={(props) => renderResponseSummary({ ...props })}
                  />
                </Grid>
              </Tooltip>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FullResponseList;
