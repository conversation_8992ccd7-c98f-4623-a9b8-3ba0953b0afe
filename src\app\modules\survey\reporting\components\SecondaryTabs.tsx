import React from "react";
import { LuChartNoAxesColumnIncreasing, LuList } from "react-icons/lu";
import GooseSvg from "../../../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/Sidebar-footer-logo.svg";
import RxReactSelect from "../../../../component/RxReactSelect";
import { useChartContext } from "../context/ChartContext";

type ChartType = "pie" | "bar" | "stackedBar";

interface ChartOption {
  type: ChartType;
  id?: number;
  category?: string;
  label: string;
}

interface SecondaryTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const SecondaryTabs: React.FC<SecondaryTabsProps> = ({
  activeTab,
  onTabChange,
}) => {
  const { selectedChartOption, setSelectedChartOption } = useChartContext();

  const tabs = [
    // {
    //   id: "goose",
    //   label: "Goose",
    //   icon: <img src={GooseSvg} alt="goose" width={16} height={16} />,
    // },
    {
      id: "summary",
      label: "Summary",
      icon: <LuChartNoAxesColumnIncreasing />,
    },
    {
      id: "responses",
      label: "Full Response List",
      icon: <LuList />,
    },
    {
      id: "viewComments",
      label: "Comments",
      icon: <LuList />,
    },
    {
      id: "allUserResponses",
      label: "View All User Responses",
      icon: <LuList />,
    },
  ];

  // Create chart type options - use the first available chart of each type
  const chartTypeOptions = [
    { label: "Pie Chart", value: { type: "pie", id: 1, label: "Pie Chart 1" } },
    // { label: "Bar Chart", value: { type: "bar", id: 3, label: "Bar Chart 1" } },
    {
      label: "Stacked Bar Chart",
      value: { type: "stackedBar", id: 1, label: "Stacked Bar Chart 1" },
    },
  ];

  // Find the currently selected chart type option
  const selectedOption = chartTypeOptions.find(
    (option) => option.value.type === selectedChartOption.type
  );

  // Handle chart selection change - NO TAB SWITCHING
  const handleChartChange = (selected: any) => {
    const typedSelected = selected;
    if (typedSelected && typedSelected.value) {
      setSelectedChartOption(typedSelected.value);
      // REMOVED: No automatic tab switching - user stays on current tab
    }
  };

  return (
    <div className="d-flex gap-3 pb-2 align-items-center justify-content-between">
      <div className="d-flex gap-3 pb-2 align-items-center justify-content-center">
        {tabs.map((tab) => (
          <div
            key={tab.id}
            className="px-3 py-2 cursor-pointer"
            onClick={() => onTabChange(tab.id)}
            style={{
              color: "var(--Rx-bg)",
              borderRadius: "8px",
              backgroundColor: "var(--Rx-btn-bg)",
              opacity: activeTab === tab.id ? "1" : "0.5",
            }}
            role="button"
          >
            {tab.icon} {tab.label}
          </div>
        ))}
      </div>

      {(activeTab === "goose" || activeTab === "summary") && (
        <div style={{ minWidth: 200 }}>
          <RxReactSelect
            options={chartTypeOptions}
            value={selectedOption}
            onChange={handleChartChange}
            placeholder="Select Chart Type"
            className="basic-select"
            styles={{
              control: (provided) => ({
                ...provided,
                minHeight: "30px",
                height: "30px",
                fontSize: "0.8rem",
                border: "none",
                boxShadow: "none",
                borderRadius: "10px",
              }),
              valueContainer: (provided) => ({
                ...provided,
                height: "30px",
                padding: "0 8px",
              }),
              indicatorsContainer: (provided) => ({
                ...provided,
                height: "30px",
              }),
              input: (provided) => ({
                ...provided,
                margin: 0,
                padding: 0,
              }),
              placeholder: (provided) => ({
                ...provided,
                fontSize: "0.8rem",
              }),
              singleValue: (provided) => ({
                ...provided,
                fontSize: "0.8rem",
              }),
            }}
          />
        </div>
      )}
    </div>
  );
};

export default SecondaryTabs;
