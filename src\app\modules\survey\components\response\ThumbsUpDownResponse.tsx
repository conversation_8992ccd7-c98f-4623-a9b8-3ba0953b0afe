import React, { useState, useEffect } from "react";
import { LuThumbsUp, LuThumbsDown } from "react-icons/lu";

interface ThumbsUpDownResponseProps {
  options: { label: string; value: string }[];
  value: string;
  onChange: (value: string) => void;
  readOnly?: boolean;
}

const iconMap: Record<string, React.ReactNode> = {
  thumb_up: <LuThumbsUp size={24} />,
  thumb_down: <LuThumbsDown size={24} />,
};

const ThumbsUpDownResponse: React.FC<ThumbsUpDownResponseProps> = ({
  options,
  value,
  onChange,
  readOnly,
}) => {
  const [selected, setSelected] = useState(value);

  useEffect(() => {
    setSelected(value);
  }, [value]);

  const handleSelect = (val: string) => {
    setSelected(val);
    onChange(val);
  };

  return (
    <div style={{ display: "flex", gap: 24 }}>
      {options.map((opt) => (
        <div
          key={opt.value}
          onClick={() => (readOnly ? null : handleSelect(opt.value))}
          style={{
            border:
              selected === opt.value ? "2px solid #007bff" : "1px solid #ccc",
            borderRadius: 8,
            padding: 16,
            cursor: readOnly ? 'not-allowed' : 'pointer',
            background: selected === opt.value ? "#e6f0ff" : "#fff",
            display: "flex",
            alignItems: "center",
            minWidth: 100,
            justifyContent: "center",
            boxShadow: selected === opt.value ? "0 0 8px #007bff33" : "none",
            color: "black",
          }}
        >
          <span style={{ marginRight: 8 }}>{iconMap[opt.value]}</span>
          <span>{opt.label}</span>
        </div>
      ))}
    </div>
  );
};

export default ThumbsUpDownResponse;
