import React, { useState, useEffect } from "react";
import { Card, Row, Col, Nav, Tab } from "react-bootstrap";
import { useParams } from "react-router-dom";
import { useSurveyData } from "../context/SurveyDataContext";

import ChartContainer from "./ChartContainer";
import FullResponseList from "./FullResponseList";
import ViewAllUserResponses from "./ViewAllUserResponses";
import ActionButtons from "./ActionButtons";
import { FilterState, SortState, ExportOptions } from "../types/chartTypes";

const SurveyReportingPage: React.FC = () => {
  const { surveyId } = useParams<{ surveyId: string }>();
  const { fetchSurveyData, isLoading, error } = useSurveyData();

  // State for filters and sorting
  const [currentFilters, setCurrentFilters] = useState<FilterState>({});
  const [currentSort, setCurrentSort] = useState<SortState>({
    field: "submittedAt",
    direction: "desc",
  });

  // Active tab state
  const [activeTab, setActiveTab] = useState<string>("summary");

  // Load survey data when component mounts
  useEffect(() => {
    if (surveyId) {
      fetchSurveyData(surveyId);
    }
  }, [surveyId, fetchSurveyData]);

  // Handle filter changes
  const handleFiltersChange = (filters: FilterState) => {
    console.log("Filters changed:", filters);
    setCurrentFilters(filters);
  };

  // Handle sort changes
  const handleSortChange = (sort: SortState) => {
    console.log("Sort changed:", sort);
    setCurrentSort(sort);
  };

  // Handle export
  const handleExport = (options: ExportOptions) => {
    console.log("Export requested:", options);
    // Implement export logic here
  };

  if (isLoading) {
    return (
      <div
        className="d-flex justify-content-center align-items-center"
        style={{ minHeight: "400px" }}
      >
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading survey data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-danger" role="alert">
        <h6 className="alert-heading">Error Loading Survey Data</h6>
        <p className="mb-0">{error}</p>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <h4 className="mb-0">Survey Reporting</h4>
            <ActionButtons
              onExport={handleExport}
              onResponseListFiltersChange={handleFiltersChange}
              onResponseListSortChange={handleSortChange}
              currentResponseListFilters={currentFilters}
              currentResponseListSort={currentSort}
              showResponseListFilter={activeTab === "responses"}
            />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="row">
        <div className="col-12">
          <Card className="rounded-5">
            <Card.Body className="p-0">
              <Tab.Container
                activeKey={activeTab}
                onSelect={(k) => setActiveTab(k || "summary")}
              >
                <Row>
                  <Col md={3} lg={2}>
                    <Nav variant="pills" className="flex-column border-end">
                      <Nav.Item>
                        <Nav.Link
                          eventKey="summary"
                          className="border-0 rounded-0 text-start"
                        >
                          <i className="fas fa-chart-pie me-2"></i>
                          Summary
                        </Nav.Link>
                      </Nav.Item>
                      <Nav.Item>
                        <Nav.Link
                          eventKey="responses"
                          className="border-0 rounded-0 text-start"
                        >
                          <i className="fas fa-table me-2"></i>
                          Responses
                        </Nav.Link>
                      </Nav.Item>
                      <Nav.Item>
                        <Nav.Link
                          eventKey="comments"
                          className="border-0 rounded-0 text-start"
                        >
                          <i className="fas fa-comments me-2"></i>
                          Comments
                        </Nav.Link>
                      </Nav.Item>
                    </Nav>
                  </Col>
                  <Col md={9} lg={10}>
                    <Tab.Content className="p-4">
                      <Tab.Pane eventKey="summary">
                        <div className="mb-3">
                          <h5 className="mb-3">
                            <i className="fas fa-chart-pie me-2"></i>
                            Survey Summary
                          </h5>
                          <p className="text-muted">
                            Charts and analytics for survey responses.
                            {Object.keys(currentFilters).length > 0 && (
                              <span className="ms-2">
                                <i className="fas fa-filter me-1"></i>
                                Filters are applied to all charts.
                              </span>
                            )}
                          </p>
                        </div>
                        <ChartContainer
                          activeTab="summary"
                          currentFilters={currentFilters}
                        />
                      </Tab.Pane>

                      <Tab.Pane eventKey="responses">
                        <div className="mb-3">
                          <h5 className="mb-3">
                            <i className="fas fa-table me-2"></i>
                            Individual Responses
                          </h5>
                          <p className="text-muted">
                            Detailed view of individual survey responses with
                            filtering and sorting capabilities.
                          </p>
                        </div>
                        <FullResponseList
                          surveyId={surveyId}
                          activeTab="responses"
                          currentFilters={currentFilters}
                        />
                      </Tab.Pane>

                      <Tab.Pane eventKey="comments">
                        <div className="mb-3">
                          <h5 className="mb-3">
                            <i className="fas fa-comments me-2"></i>
                            Comment Responses
                          </h5>
                          <p className="text-muted">
                            View all comment-type question responses with
                            timestamps.
                            {Object.keys(currentFilters).length > 0 && (
                              <span className="ms-2">
                                <i className="fas fa-filter me-1"></i>
                                Filters are applied to comments.
                              </span>
                            )}
                          </p>
                        </div>
                        <ViewAllUserResponses
                          surveyId={surveyId}
                          currentFilters={currentFilters}
                        />
                      </Tab.Pane>
                    </Tab.Content>
                  </Col>
                </Row>
              </Tab.Container>
            </Card.Body>
          </Card>
        </div>
      </div>

      {/* Filter Status Display */}
      {Object.keys(currentFilters).length > 0 && (
        <div className="row mt-3">
          <div className="col-12">
            <div className="alert alert-info" role="alert">
              <div className="d-flex justify-content-between align-items-center">
                <div>
                  <i className="fas fa-filter me-2"></i>
                  <strong>Active Filters:</strong>
                  <span className="ms-2">
                    {Object.entries(currentFilters).map(([key, value]) => (
                      <span key={key} className="badge bg-primary me-1">
                        {key}:{" "}
                        {Array.isArray(value)
                          ? value.join(", ")
                          : String(value)}
                      </span>
                    ))}
                  </span>
                </div>
                <button
                  className="btn btn-sm btn-outline-secondary"
                  onClick={() => setCurrentFilters({})}
                >
                  <i className="fas fa-times me-1"></i>
                  Clear All
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SurveyReportingPage;
