import React, { useEffect, useMemo, useState } from "react";
import {
  emojiOptions,
  ResponseType,
  thumbsOptions,
} from "./util/constant";
import QuestionResponseWrapper from "./QuestionResponseWrapper";
import MultipleChoiceResponse from "../response/MultipleChoiceResponse";
import ScaleResponse from "../response/ScaleResponse";
import YesNoResponse from "../response/YesNoResponse";
import ThumbsUpDownResponse from "../response/ThumbsUpDownResponse";
import EmojiResponse from "../response/EmojiResponse";
import RatingResponse from "../response/RatingResponse";
import NpsResponse from "../response/NpsResponse";
import CommentResponse from "../response/CommentResponse";
import { ISubmitSurveyResponsePayload, ISurveyQuestionListItem } from "../../../../apis/type";

interface Props {
  question: ISurveyQuestionListItem;
  readOnly?: boolean;
  questionNumber?: number;
  onAnswerChange?: (answer: ISubmitSurveyResponsePayload) => void;
  initialAnswer?: ISubmitSurveyResponsePayload;
  onUploadingStateChange?: (isUploading: boolean) => void;
  showValidationError?: boolean;
  validationErrorMessage?: string;
}

const QuestionResponseViewer: React.FC<Props> = ({
  question,
  questionNumber,
  readOnly,
  onAnswerChange,
  initialAnswer,
  onUploadingStateChange,
  showValidationError = false,
  validationErrorMessage = '',
}) => {
  const options = useMemo(() => {
    return (question.options || []).map((opt) => ({
      label: typeof opt === 'string' ? opt : opt.optionText,
      value: typeof opt === 'string' ? opt : opt.optionValue,
    }));
  }, [question.options]);

  // Derive scale values from options for SCALE response type
  const scaleValues = useMemo(() => {
    if (question.responseType === ResponseType.SCALE && question.options && question.options.length > 0) {
      const numericValues = question.options
        .map(opt => typeof opt === 'string' ? parseInt(opt) : parseInt(opt.optionValue))
        .filter(val => !isNaN(val))
        .sort((a, b) => a - b);

      return {
        startScale: numericValues[0] || 0,
        endScale: numericValues[numericValues.length - 1] || 5
      };
    }
    // Default scale values if no options available
    return { startScale: 0, endScale: 5 };
  }, [question.options, question.responseType]);

  // Derive rating values from options for RATING response type
  const ratingValues = useMemo(() => {
    if (question.responseType === ResponseType.RATING && question.options && question.options.length > 0) {
      const numericValues = question.options
        .map(opt => typeof opt === 'string' ? parseInt(opt) : parseInt(opt.optionValue))
        .filter(val => !isNaN(val));

      return {
        ratting: Math.max(...numericValues) || 5
      };
    }
    // Default rating value if no options available
    return { ratting: 5 };
  }, [question.options, question.responseType]);

  // Derive NPS values from options for NET_PROMOTER_SCORE response type
  const npsValues = useMemo(() => {
    if (question.responseType === ResponseType.NET_PROMOTER_SCORE && question.options && question.options.length > 0) {
      const numericValues = question.options
        .map(opt => typeof opt === 'string' ? parseInt(opt) : parseInt(opt.optionValue))
        .filter(val => !isNaN(val))
        .sort((a, b) => a - b);

      return {
        startNetPromoterScore: numericValues[0] || 0,
        endNetPromoterScore: numericValues[numericValues.length - 1] || 10
      };
    }
    // Default NPS range if no options available
    return {
      startNetPromoterScore: 0,
      endNetPromoterScore: 10
    };
  }, [question.options, question.responseType]);

  // Local state for value - initialize with initial answer if provided
  const [responseValue, setResponseValue] = useState<string | number>("");

  // Local state for comment
  const [comment, setComment] = useState<string>(
    initialAnswer?.comment || ""
  );

  // Local state for branching comment
  const [branchingComment, setBranchingComment] = useState<string>(
    initialAnswer?.branchingQuestion?.comment || ""
  );

  // Local state for attachments
  const [attachments, setAttachments] = useState<Array<{
    attachmentId: string;
    attachmentUrl: string;
    attachmentType: string;
    attachmentName?: string;
  }>>(
    initialAnswer?.attachmentDtoList?.map(att => ({
      attachmentId: att.attachmentId,
      attachmentUrl: att.attachmentUrl,
      attachmentType: att.attachmentType,
    })) || []
  );

  const [branchingAttachments, setBranchingAttachments] = useState<Array<{
    attachmentId: string;
    attachmentUrl: string;
    attachmentType: string;
    attachmentName?: string;
  }>>(
    initialAnswer?.branchingQuestion?.attachmentDtoList?.map(att => ({
      attachmentId: att.attachmentId,
      attachmentUrl: att.attachmentUrl,
      attachmentType: att.attachmentType,
    })) || []
  );

  // State to track uploading status for both main and branching attachments
  const [isMainAttachmentUploading, setIsMainAttachmentUploading] = useState(false);
  const [isBranchingAttachmentUploading, setIsBranchingAttachmentUploading] = useState(false);

  // Track overall uploading state and notify parent
  useEffect(() => {
    if (onUploadingStateChange) {
      const isUploading = isMainAttachmentUploading || isBranchingAttachmentUploading;
      onUploadingStateChange(isUploading);
    }
  }, [isMainAttachmentUploading, isBranchingAttachmentUploading, onUploadingStateChange]);

  // Reset states only when question ID changes (new question)
  useEffect(() => {
    // Reset response value
    setResponseValue(initialAnswer?.answers?.[0] || "");

    // Reset comment
    setComment(initialAnswer?.comment || "");

    // Reset branching comment
    setBranchingComment(initialAnswer?.branchingQuestion?.comment || "");

    // Reset attachments
    setAttachments(
      initialAnswer?.attachmentDtoList?.map(att => ({
        attachmentId: att.attachmentId,
        attachmentUrl: att.attachmentUrl,
        attachmentType: att.attachmentType,
      })) || []
    );

    // Reset branching attachments
    setBranchingAttachments(
      initialAnswer?.branchingQuestion?.attachmentDtoList?.map(att => ({
        attachmentId: att.attachmentId,
        attachmentUrl: att.attachmentUrl,
        attachmentType: att.attachmentType,
      })) || []
    );

    // Reset uploading states when question changes
    setIsMainAttachmentUploading(false);
    setIsBranchingAttachmentUploading(false);
  }, [question.id]); // Only depend on question.id, not initialAnswer

  // Load initial data when initialAnswer changes, but only if current states are empty
  // This prevents overwriting user input during attachment uploads
  useEffect(() => {
    if (initialAnswer) {
      // For COMMENT response type, load comment from answers array
      if (question.responseType === ResponseType.COMMENT) {
        if (!comment || comment === "") {
          setComment(initialAnswer?.answers?.[0] || "");
        }
      } else {
        // For other response types, load response value from answers array
        // Check if responseValue is empty (handle both string and number types)
        const isResponseValueEmpty = responseValue === "" || responseValue === null || responseValue === undefined;

        if (isResponseValueEmpty) {
          const answerValue = initialAnswer?.answers?.[0];

          // Convert to appropriate type based on response type
          if (question.responseType === ResponseType.SCALE ||
              question.responseType === ResponseType.RATING ||
              question.responseType === ResponseType.NET_PROMOTER_SCORE) {
            // Convert to number for numeric response types
            const numericValue = answerValue ? parseFloat(answerValue) : undefined;
            if (!isNaN(numericValue as number)) {
              setResponseValue(numericValue as number);
            }
          } else {
            // Keep as string for other response types
            setResponseValue(answerValue || "");
          }
        }

        // Only update comment if current comment is empty
        if (!comment || comment === "") {
          setComment(initialAnswer?.comment || "");
        }
      }

      // Only update branching comment if current branching comment is empty
      if (!branchingComment || branchingComment === "") {
        setBranchingComment(initialAnswer?.branchingQuestion?.comment || "");
      }

      // Always update attachments from initialAnswer (they come from saved data)
      const initialAttachments = initialAnswer?.attachmentDtoList?.map(att => ({
        attachmentId: att.attachmentId,
        attachmentUrl: att.attachmentUrl,
        attachmentType: att.attachmentType,
      })) || [];

      const initialBranchingAttachments = initialAnswer?.branchingQuestion?.attachmentDtoList?.map(att => ({
        attachmentId: att.attachmentId,
        attachmentUrl: att.attachmentUrl,
        attachmentType: att.attachmentType,
      })) || [];

      // Only update if different from current state (to avoid unnecessary re-renders)
      if (JSON.stringify(attachments) !== JSON.stringify(initialAttachments)) {
        setAttachments(initialAttachments);
      }

      if (JSON.stringify(branchingAttachments) !== JSON.stringify(initialBranchingAttachments)) {
        setBranchingAttachments(initialBranchingAttachments);
      }
    }
  }, [initialAnswer]); // Depend on initialAnswer and responseType
  // }, [initialAnswer, question.responseType]); // Depend on initialAnswer and responseType

  // // Initialize responseValue on component mount if initialAnswer is available
  // useEffect(() => {
  //   if (initialAnswer && initialAnswer.answers && initialAnswer.answers.length > 0) {
  //     const answerValue = initialAnswer.answers[0];

  //     if (question.responseType === ResponseType.COMMENT) {
  //       setComment(answerValue || "");
  //     } else if (question.responseType === ResponseType.SCALE ||
  //                question.responseType === ResponseType.RATING ||
  //                question.responseType === ResponseType.NET_PROMOTER_SCORE) {
  //       // Convert to number for numeric response types
  //       const numericValue = answerValue ? parseFloat(answerValue) : undefined;
  //       if (!isNaN(numericValue as number)) {
  //         setResponseValue(numericValue as number);
  //       }
  //     } else {
  //       // Keep as string for other response types
  //       setResponseValue(answerValue || "");
  //     }
  //   }
  // }, []); // Run only on mount

  // Helper function to create the answer payload
  const createAnswerPayload = (
    answers: string[],
    commentValue: string,
    attachmentList: typeof attachments,
    branchingAttachmentList: typeof branchingAttachments,
    branchingCommentValue?: string
  ): ISubmitSurveyResponsePayload => {
    return {
      answers,
      comment: commentValue,
      surveyId: question.surveyId || '',
      surveyQuestionId: question.id || '',
      attachmentDtoList: attachmentList.map(att => ({
        attachmentId: att.attachmentId,
        attachmentUrl: att.attachmentUrl,
        attachmentType: att.attachmentType,
      })),
      branchingQuestion: question.allowBranching ? {
        answers: [], // TODO: Handle branching answers
        comment: branchingCommentValue || branchingComment,
        surveyQuestionId: question.branchingQuestion?.id || '',
        attachmentDtoList: branchingAttachmentList.map(att => ({
          attachmentId: att.attachmentId,
          attachmentUrl: att.attachmentUrl,
          attachmentType: att.attachmentType,
        })),
      } : null,
    };
  };

  // Handler for response value changes
  const handleResponseValueChange = (value: string | number) => {
    setResponseValue(value);
    if (onAnswerChange) {
      const answers = value ? [value.toString()] : [];
      const payload = createAnswerPayload(answers, comment, attachments, branchingAttachments, branchingComment);
      onAnswerChange(payload);
    }
  };

  // Handler for comment changes
  const handleCommentChange = (newComment: string) => {
    setComment(newComment);
    if (onAnswerChange) {
      // For COMMENT response type, put the comment content in answers array
      if (question.responseType === ResponseType.COMMENT) {
        const answers = newComment ? [newComment] : [];
        const payload = createAnswerPayload(answers, "", attachments, branchingAttachments, branchingComment);
        onAnswerChange(payload);
      } else {
        // For other response types, keep the existing behavior
        const answers = responseValue ? [responseValue.toString()] : [];
        const payload = createAnswerPayload(answers, newComment, attachments, branchingAttachments, branchingComment);
        onAnswerChange(payload);
      }
    }
  };

  const handleBranchingCommentChange = (newComment: string) => {
    setBranchingComment(newComment);
    if (onAnswerChange) {
      const answers = responseValue ? [responseValue.toString()] : [];
      const payload = createAnswerPayload(answers, comment, attachments, branchingAttachments, newComment);
      onAnswerChange(payload);
    }
  };

  // Handler for attachment changes
  const handleAttachmentsChange = (newAttachments: typeof attachments) => {
    setAttachments(newAttachments);
    if (onAnswerChange) {
      // For COMMENT response type, use comment content in answers array
      if (question.responseType === ResponseType.COMMENT) {
        const answers = comment ? [comment] : [];
        const payload = createAnswerPayload(answers, "", newAttachments, branchingAttachments, branchingComment);
        onAnswerChange(payload);
      } else {
        const answers = responseValue ? [responseValue.toString()] : [];
        const payload = createAnswerPayload(answers, comment, newAttachments, branchingAttachments, branchingComment);
        onAnswerChange(payload);
      }
    }
  };

  // Handler for branching attachment changes
  const handleBranchingAttachmentsChange = (newBranchingAttachments: typeof branchingAttachments) => {
    setBranchingAttachments(newBranchingAttachments);
    if (onAnswerChange) {
      // For COMMENT response type, use comment content in answers array
      if (question.responseType === ResponseType.COMMENT) {
        const answers = comment ? [comment] : [];
        const payload = createAnswerPayload(answers, "", attachments, newBranchingAttachments, branchingComment);
        onAnswerChange(payload);
      } else {
        const answers = responseValue ? [responseValue.toString()] : [];
        const payload = createAnswerPayload(answers, comment, attachments, newBranchingAttachments, branchingComment);
        onAnswerChange(payload);
      }
    }
  };

  // const handleAnd

  const renderView = () => {
    switch (question.responseType) {
      case ResponseType.MULTIPLE_CHOICE:
        return (
          <MultipleChoiceResponse
            options={options}
            value={typeof responseValue === "string" ? responseValue : ""}
            onChange={(value) => {
              handleResponseValueChange(value);
            }}
            readOnly={readOnly}
          />
        );
      case ResponseType.SCALE:
        return (
          <ScaleResponse
            startScale={scaleValues.startScale}
            endScale={scaleValues.endScale}
            value={
              typeof responseValue === "number" ? responseValue : undefined
            }
            onChange={handleResponseValueChange}
            readOnly={readOnly}
          />
        );
      case ResponseType.YES_NO:
        return (
          <YesNoResponse
            options={options}
            value={typeof responseValue === "string" ? responseValue : ""}
            onChange={handleResponseValueChange}
            readOnly={readOnly}
          />
        );
      case ResponseType.THUMBS_UP_DOWN:
        return (
          <ThumbsUpDownResponse
            options={thumbsOptions}
            value={typeof responseValue === "string" ? responseValue : ""}
            onChange={handleResponseValueChange}
            readOnly={readOnly}
          />
        );
      case ResponseType.EMOJI:
        return (
          <EmojiResponse
            options={emojiOptions}
            value={typeof responseValue === "string" ? responseValue : ""}
            onChange={handleResponseValueChange}
            readOnly={readOnly}
          />
        );
      case ResponseType.RATING:
        return (
          <RatingResponse
            ratting={ratingValues.ratting}
            rattingIcon={question.rattingIcon as "smiley" | "star" | "thumb_up"}
            value={typeof responseValue === "number" ? responseValue : 0}
            onChange={handleResponseValueChange}
            readOnly={readOnly}

          />
        );
      case ResponseType.NET_PROMOTER_SCORE:
        return (
          <NpsResponse
            startNetPromoterScore={npsValues.startNetPromoterScore}
            endNetPromoterScore={npsValues.endNetPromoterScore}
            value={typeof responseValue === "number" ? responseValue : 0}
            onChange={handleResponseValueChange}
            readOnly={readOnly}
          />
        );
      case ResponseType.COMMENT:
        return (
          <CommentResponse
            value={comment}
            onChange={handleCommentChange}
            readOnly={readOnly}
          />
        );
      default:
        return <></>;
    }
  };

  return (
    <div>
      <QuestionResponseWrapper
        questionText={question.questionText}
        isRequired={question.isRequired}
        allowAttachment={question.allowAttachment}
        allowedAttachmentType={question.attachmentType}
        attachments={attachments}
        onAttachmentsChange={handleAttachmentsChange}
        allowComment={question.allowComment}
        comment={comment}
        onCommentChange={handleCommentChange}
        allowBranching={question.allowBranching}
        branchingQuestionText={question?.branchingQuestion?.questionText} // <-- updated property name
        allowbranchingAttchement={question.branchingQuestion?.allowAttachment}
        branchingAttchementType={question.branchingQuestion?.attachmentType}
        branchingAttachments={branchingAttachments}
        onBranchingAttachmentsChange={handleBranchingAttachmentsChange}
        onUploadingStateChange={setIsMainAttachmentUploading}
        onBranchingUploadingStateChange={setIsBranchingAttachmentUploading}
        questionNumber={questionNumber}
        readOnly={readOnly}
        id={question.id}
        onBranchingCommentChange={handleBranchingCommentChange}
        branchingQuestionComment={branchingComment}
      >
        {renderView()}
      </QuestionResponseWrapper>

      {/* Validation Error Display */}
      {/* {showValidationError && validationErrorMessage && (
        <p className="text-danger text-center mt-4" style={{ fontSize: '15px' }}>
          {validationErrorMessage}
        </p>
      )} */}
    </div>
  );
};

export default QuestionResponseViewer;