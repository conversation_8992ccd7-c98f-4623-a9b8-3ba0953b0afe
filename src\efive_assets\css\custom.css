@import url("./attahcmentUI.css");
@import url("./inspection-report.css");
@import url("./util.css");

/* start theme variable */
[data-bs-theme="light"] {
  --card-bg-body: #f5f5f5;
  --border-companylight: #000000;
  --table-header: #0d0e12;
  --table-body: #585858;
  --btn-dark-bg: #0066cd;
  --btn-dark-color: #f5f5f5;
  --card-text-color: #0d0e12;
  --card-label-color: #252f4a;
  --btn-bg: #0d0e12;
  --plceholder-color: #585858;
  --btn-color: #f5f5f5;
  --scrollbar-color: #0066cd;
  --disabled-bg: #c6c6c6;
  --sidebar-menu-bg-light: #ffffff;
  --sidebar-menu-bg-dark: #0066cd;

  --Rx-title: #030303;
  --login-bg-img: url("../../efive_assets/images/login_bg.jpg");
  --Black-2: #eaeaea;
  --Black-3: #dadada;
  --Gray-1: #737373;
  --Button-shadow: 0px 4px 4px 0px rgba(239, 231, 215, 0.25);
  --rx-sidenav-help: #f6f6f6;
  --rx-sidenav-title: #355f9b;
  --rx-sidenav-footer-content: #030303;
  --rx-sidenav-header-bg: #ffffff;
  --rx-sidenav-link-bg: #355f9b;
  --rx-sidenav-link: #030303;
  --Rx-bg: #ffffff;
  --Rx-btn-bg: #355f9b;
  --Rx-btn-bg-reverse: #ffffff;
  --Rx-15-F6-color: #f6f6f6;
  --Rx-35blue-15black: #355f9b;
  --Rx-35blue-15black-reverse: #151515;
  --Rx-FFwhite-8Cgray: #8c8c8c;
  --Rx-25black-72ligtblue: #72b7dd;
  --Rx-white-72ligtblue: #72b7dd;
  --Rx-03black-white: #ffffff;
  --Rx-15black-ECwhite: #ececec;
  --Rx-35blue-white: #355f9b;
  --Rx-25black-E1gray: #e1e1e1;
  --rxb: 0, 0, 0;
  --Chat-K: #fafafa;
  --Send-msg: #355f9b;
  --light-white: #1e1d1dc1;

  --modal-fade: #00000078;

  /* chat */
  --bg-self-message: #0066cd;
  --bg-message: #d7d7d7;
  --message-self-text: #ffffff;
  --message-text: #4e4e4e;
  --avatar-border: #d7d7d7;
  --author-name: #355f9b;
  --mention-input-br: #99a1b7;
  --mention-input-br-light: #00000024;
  --mention-input-bg: #f5f5f5;
  --send-file-wrapper-bg: #e4e4e4;
  --attachment-tile-bg: #355f9b;

  /* react-select */
  --r-select-control-bg: #ffffff;
  --r-select-border: #ced4da;
  --r-select-color: #333333;
  --r-select-menu-bg: #ffffff;
  --r-select-option-focus: #355f9b;
  --r-select-multi-chip-bg: #e4e4e4;
  --r-select-remove-bg: #ff7875;
  --r-select-highlight: #f0f0f0;
  --r-select-primary: #2684ff;
  --r-select-primary25: #f0f0f0;

  /* stpper */
  --stepper-bg: #ffffff;
  --stepper-color: #000000;
  --stepper-current-bg: #7c7c7c;
  --stepper-active-bg: #355f9b;
  --stepper-active-color: #ffffff;
  --stepper-border-color: #d3d3d3;

  --in-card: #fafafa;
  --ticket-badge: #000;

  --goosechat-question: text #f0f0f0;

  /* survey */
  --chart-text: #000000;
  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-bg-wrapper: #dadada21;

}

.splash-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f0f0;
}

.w-180px {
  width: 180px !important;
}

.splash-content {
  padding: 20px;
  border-radius: 8px;
}

[data-bs-theme="dark"] {
  --card-bg-body: #0d0e12;
  --border-companylight: #ffffff;
  --table-header: #f5f5f5;
  --table-body: #9a9cae;
  --btn-dark-bg: #f5f5f5;
  --btn-dark-color: #0d0e12;
  --card-text-color: #f5f5f5;
  --card-label-color: #9a9cae;
  --btn-bg: #f5f5f5;
  --plceholder-color: #9a9cae;
  --btn-color: #0d0e12;
  --scrollbar-color: #f5f5f5;
  --disabled-bg: #323232;
  --Rx-bg: #000000;
  --Rx-btn-bg: #ffffff;
  --Rx-title: #ffffff;
  --login-bg-img: url("../../efive_assets/images/Backgroud.png");
  --Black-2: #151515;
  --Black-3: #252525;
  --Gray-1: #8c8c8c;
  --Button-shadow: 0px 4px 4px 0px rgba(16, 24, 40, 0.25);
  --rx-sidenav-help: #1c1c1c;
  --rx-sidenav-title: #ffffff;
  --rx-sidenav-footer-content: #7d7d7d;
  --rx-sidenav-header-bg: #151515;
  --rx-sidenav-link-bg: #252525;
  --rx-sidenav-link: #7d7d7d;
  --Rx-bg: #000000;
  --Rx-btn-bg: #ffffff;
  --Rx-btn-bg-reverse: #355f9b;
  --Rx-15-F6-color: #151515;
  --Rx-35blue-15black: #151515;
  --Rx-35blue-15black-reverse: #355f9b;
  --Rx-FFwhite-8Cgray: #ffffff;
  --Rx-25black-72ligtblue: #252525;
  --Rx-white-72ligtblue: white;

  --Rx-03black-white: #030303;
  --Rx-15black-ECwhite: #151515;
  --Rx-35blue-white: white;
  --Rx-25black-E1gray: #252525;
  --rxb: 0, 0, 0;
  --Chat-K: #252525;
  --Send-msg: #151515;
  --light-white: #6d6d6dc1;
  --modal-fade: #1f1f1fc1;

  /* chat */
  --bg-self-message: #0071d9;
  --bg-message: #3e4649;
  --message-self-text: #ffffff;
  --message-text: #ffffff;
  --avatar-border: #3e4649;
  --author-name: #48a3ff;
  --mention-input-br: #ffffff;
  --mention-input-br-light: #00000024;
  --mention-input-bg: #f0f8ff3d;
  --send-file-wrapper-bg: #797979;
  --attachment-tile-bg: #111;

  /* react-select */
  --r-select-control-bg: #1e1e1e;
  --r-select-border: #3a3a3a;
  --r-select-color: #ffffff;
  --r-select-menu-bg: #2c2c2c;
  --r-select-option-focus: #3a3a3a;
  --r-select-multi-chip-bg: #333333;
  --r-select-remove-bg: #ff4d4f;
  --r-select-highlight: #2c2c2c;
  --r-select-primary: #3a3a3a;
  --r-select-primary25: #2c2c2c;

  /* stpper */
  --stepper-bg: #1e1e1e;
  --stepper-color: #f5f5f5;
  --stepper-current-bg: #ffffff;
  --stepper-active-bg: #007bff;
  --stepper-active-color: #ffffff;
  --stepper-border-color: #555555;

  --in-card: #15171C;
  --ticket-badge: #ffffff;

  --goosechat-question: text #000000;

  /* survey */
  --chart-text: #ffffff;

  /*survey-fill-up*/
  --glass-bg: rgba(30, 32, 38, 0.85);
  --glass-bg-wrapper: #7474741e;
}

.modal {
  --bs-modal-zindex: 1010;
}

.modal-backdrop {
  --bs-backdrop-zindex: 1010;

  --color-text: #fff !important;
  --ck-editor-header-collapse: #808080 !important;
  --ql-body-bg: #66686f69 !important;
  --ql-header-bg: #47474759 !important;
  --ck-editor-body: #acacac !important;
  --active_menu-bg: #414141 !important;
}

/* end theme variable */

body {
  font-family: "Inter", sans-serif !important;
  background-color: var(--Rx-bg) !important;
}

.app-main {
  background-color: var(--Rx-bg) !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  padding-bottom: 39px;
}

div {
  font-family: "Inter", sans-serif !important;
}

/* 
.app-content{
  max-height: calc(100vh - 75px) !important;
  overflow-y: auto !important;
} */

/* start logn css */
.login-bg {
  background-image: var(--login-bg-img);
  background-repeat: no-repeat !important;
  background-size: cover !important;
}

.login-page-height {
  height: 100vh !important;
}

.welcome-text {
  color: var(--Rx-title);
  text-align: center;
  font-size: 40px;
  font-style: normal;
  font-weight: 500;
  line-height: 56px;
}

.sign-up-text {
  color: #989898 !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

.forgot-text {
  color: var(--Rx-btn-bg);
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
}

.error-msg {
  font-size: 11px !important;
  font-weight: 500 !important;
  color: rgba(251, 93, 93, 1) !important;
  margin-top: 3px !important;
}

.btn-success {
  padding: 9px 20px !important;
  font-size: 16px !important;
  font-style: normal !important;
  line-height: 24px !important;
  border-radius: 8px !important;
  border: 1px solid var(--bs-success) !important;
  background-color: var(--bs-success) !important;
}

.rx-reset-btn {
  background: var(--Rx-btn-bg) !important;
  color: var(--Rx-bg) !important;
  padding: 2px 10px !important;
  font-size: 14px !important;
  font-style: normal !important;
  line-height: 24px !important;
  border-radius: 8px !important;
  border: 1px solid transparent !important;
}

.rx-btn {
  background: var(--Rx-btn-bg) !important;
  color: var(--Rx-bg) !important;
  padding: 9px 20px !important;
  font-size: 16px !important;
  font-style: normal !important;
  line-height: 24px !important;
  border-radius: 8px !important;
  border: 1px solid transparent !important;
}

.rx-btn-disabled {
  opacity: 0.5 !important;
  background: var(--Rx-btn-bg) !important;
  color: var(--Rx-bg) !important;
}

.rx-btn:hover {
  background-color: var(--Rx-15-F6-color) !important;
  border: 1px solid var(--Rx-btn-bg) !important;
  color: var(--Rx-btn-bg) !important;
  padding: 9px 20px;
  font-size: 16px;
  font-style: normal;
  line-height: 24px;
  border-radius: 8px;
}

.btn-blue-black {
  background-color: var(--Rx-35blue-15black) !important;
  color: white !important;
  padding: 9px 20px;
  font-size: 16px;
  font-style: normal;
  line-height: 24px;
  border-radius: 8px;
  border: 1px solid var(--Rx-35blue-15black) !important;
}

/* .btn-blue-black:hover {
  background-color: var(--Rx-35blue-15black-reverse) !important;
  color: white !important;
} */
.btn-black-lightblue {
  background-color: var(--Rx-25black-72ligtblue) !important;
  color: white !important;
  padding: 9px 20px;
  font-size: 16px;
  font-style: normal;
  line-height: 24px;
  border-radius: 8px;
  border: 1px solid var(--Rx-white-72ligtblue) !important;
}

.btn-black-lightblue:hover {
  color: var(--Rx-white-72ligtblue) !important;
  padding: 9px 20px;
  font-size: 16px;
  font-style: normal;
  line-height: 24px;
  border-radius: 8px;
  border: 1px solid var(--Rx-25black-72ligtblue) !important;
}

.rx-outline-btn {
  background-color: var(--Rx-35blue-15black) !important;
  color: white !important;
  padding: 9px 20px;
  font-size: 16px;
  font-style: normal;
  line-height: 24px;
  border-radius: 8px;
  border: 1px solid var(--Rx-35blue-15black) !important;
}

.bg-orange {
  background-color: #fc9403 !important;
  color: var(--ticket-badge) !important;
  width: 19px;
  height: 19px;
  font-size: 10px;
}

.Rx-btn-bg {
  color: var(--rx-sidenav-header-bg) !important;
  background-color: var(--Rx-btn-bg) !important;
  /* width: 19px;
  height: 19px;
  font-size: 10px; */
}


/* .btn-black-lightgray {
  background-color: var(--Rx-25black-E1gray) !important;
  color: var(--Rx-title) !important;
  padding: 9px 20px;
  font-size: 16px;
  font-style: normal;
  line-height: 24px;
  border-radius: 8px;
  border: 1px solid var(--Rx-25black-E1gray);
} */

.btn:disabled,
.btn.disabled,
fieldset:disabled .btn {
  opacity: 0.2;
  background: var(--Rx-bg);
}

.border-0 {
  border-radius: 0 !important;
}

/* .btn:hover {
  background: var(--Rx-bg, #fff) !important;
  opacity: 1 !important;
  opacity: 1 !important;
} */

/* .btn.btn-login-page:focus-visible {
  color: #0066cd !important;
  background-color: #f5f5f5 !important;
  border: 1px solid #0066cd !important;
  outline: 0 !important;
  box-shadow: none !important;
} */

.password-popover {
  width: 250px;
}

.password-popover ul {
  padding-left: 0px;
  list-style: none;
}

.password-popover ul li {
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  margin-top: 8px;
}

.password-popover ul li img {
  width: 12px;
  height: 12px;
  margin-right: 6px;
}

.password-popover .popover-content {
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
}

.custom-progress-bar {
  height: 8px !important;
}

/* .password-popover .popover-body {
  background-color: #212529;
  color: white;
} */

/* .password-popover .arrow::before {
  border-bottom-color: #343a40;
  border-top-color: #343a40;
  border-left-color: #343a40;
  border-right-color: #343a40;
} */

.pw_hide_show svg {
  font-size: 13px !important;
  font-weight: 500 !important;
  color: var(--Rx-title) !important;
  position: absolute !important;
  right: 12px !important;
  top: 16px !important;
  bottom: 13px !important;
  cursor: pointer !important;
  z-index: 4 !important;
  text-decoration: underline !important;
}

.login-left {
  border-radius: 0px 100px 100px 0px !important;
  /* background-color: #eeeeee !important; */
  background-color: var(--Black-2) !important;
  box-shadow: 2px 0px 4px 0px rgba(0, 0, 0, 0.25) !important;
  width: 40% !important;
}

.inlogin-footre-version {
  display: none !important;
}

.form-width {
  /* margin-top: -60px; */
  width: 480px;
}

/* .form-width-factor {
  margin-top: -60px; 
   width: 600px;
} */

.twofa_div {
  margin-top: -60px;
}

.flex-center {
  justify-content: center !important;
  align-items: center !important;
}

.about-product {
  border-radius: 15px !important;
  background: rgba(255, 255, 255, 1) !important;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25) !important;
  height: 230px !important;
  padding: 47px 0px 47px 0px !important;
  display: flex !important;
  align-items: end !important;
  justify-content: center !important;
  top: 118px !important;
  position: absolute !important;
  width: 100% !important;
}

.logo {
  position: absolute !important;
  width: 200px !important;
  height: 200px !important;
  top: -101px !important;
  z-index: 1 !important;
}

.login-right {
  width: 60% !important;
}

.right-footer span {
  font-size: 18px;
  color: var(--Gray-1);
}

.right-footer-text {
  font-size: 16px !important;
  font-weight: 500 !important;
  color: var(--Rx-btn-bg) !important;
  line-height: 24px;
  margin-left: 6px !important;
}

.right-main-header {
  color: var(--Rx-title) !important;
  margin-bottom: 20px;
  font-size: 40px;
  font-style: normal;
  font-weight: 500;
  line-height: 56px;
  margin-bottom: 20px !important;
}

.otp-heade-one {
  font-size: 24px !important;
  font-weight: 600 !important;
  line-height: 36px !important;
  color: var(--Rx-title) !important;
  margin-bottom: 20px !important;
}

.otp-heade-two {
  color: var(--Gray-1) !important;
  font-size: 18px !important;
}

.otp-heade-two span {
  color: var(--Rx-btn-bg) !important;
}

.otp-count {
  color: #171717 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

.otp-separator {
  width: 12px !important;
}

.otpdiv>div {
  color: var(--Rx-btn-bg) !important;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 28px;
}

.otp-field input {
  color: var(--Rx-btn-bg) !important;
  background-color: var(--Rx-bg) !important;
}

.spinner>span>span {
  background-color: var(--Rx-btn-bg) !important;
}

.otpdiv>div>span>span {
  color: var(--Gray-1);
}

.otpdiv>div>span {
  color: var(--btn-dark-bg) !important;
}

.otpdiv>.re-send-button {
  background-color: transparent !important;
  border: none !important;
}

.otpdiv>div>button>span,
.otpdiv>.re-send-button>span {
  color: var(--Gray-1) !important;
  font-size: 18px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 28px !important;
}

.contact-text {
  color: var(--Rx-bg, var(--Color-ResolvedX-White, #fff)) !important;
  font-size: 16px;
  margin-left: 6px;
}

.left-footer-text {
  color: var(--Rx);
  text-align: center;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 28px;
}

.image-card {
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
}

.logo-image {
  height: 200px !important;
  width: 200px !important;
  position: relative !important;
  top: 12px !important;
  z-index: 9 !important;
}

.product-card {
  border-radius: 15px !important;
  background: var(--Black-3) !important;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25) !important;
  width: 440px !important;
  position: relative;
  top: -97px;
  padding: 109px 0px 32px 0px !important;
  display: flex !important;
  align-items: end !important;
  justify-content: center !important;
  text-align: center !important;
}

.product-dsc p {
  color: var(--Rx-title);
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
}

a:focus-visible {
  outline: none !important;
  border: 0px !important;
}

.flex-center {
  justify-content: center !important;
  align-items: center !important;
}

.form-control:focus {
  outline: none !important;
  border: 1px solid var(--Rx-35blue-white) !important;
  outline: 0 !important;
  box-shadow: none !important;
}

.form-control:hover {
  outline: none !important;
  border: 1px solid var(--Rx-35blue-white) !important;
  outline: 0 !important;
  box-shadow: none !important;
}

.input-space {
  margin-top: 14px;
}

.input-space-checkbox {
  margin-bottom: 0px;
}

.k-dropdown-custom-popup {
  width: max-content !important;
}

.k-dropdown-custom-popup .k-list-item {
  font-size: 10px !important;
}

.textarea-form-control:focus-within {
  border: 1px solid var(--Rx-35blue-white) !important;
}

.textarea-form-control:hover {
  border: 1px solid var(--Rx-35blue-white) !important;
}

.msgicon {
  width: 100px !important;
  height: 100px !important;
}

.msg-text {
  color: #1e1e1e !important;
  font-size: 32px !important;
  font-weight: 500 !important;
}

.login-right-footer-mobile {
  display: none !important;
}

.danger-circle {
  height: 10px;
  width: 10px;
  background-color: red;
  border-radius: 100%;
}

.success-circle {
  height: 10px;
  width: 10px;
  background-color: green;
  border-radius: 100%;
  align-items: center !important;
}

.need-help-text {
  position: absolute;
  top: 22px;
  right: 18px;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  display: flex;
  align-items: center;
  color: var(--Rx-btn-bg);
}

.need-help-icon {
  width: 20px;
  height: 20px;
  margin-right: 6px;
}

.spinner-img {
  width: 32px;
  height: 32px;
  animation-name: spinner;
  animation-duration: 2s;
  animation-iteration-count: infinite;
}

@keyframes spinner {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* end logn css */

/* Start Forgot Css */
.forgotpassword-text p {
  color: var(--Gray-1);
  /* margin-bottom: 30px; */
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
}

/* End Forgot Css */
/* start header  */
[data-kt-app-header-fixed="true"][data-kt-app-sidebar-fixed="true"][data-kt-app-sidebar-push-header="true"] .app-header,
[data-kt-app-header-fixed="true"][data-kt-app-sidebar-sticky="on"][data-kt-app-sidebar-push-header="true"] .app-header {
  border-bottom: 1px solid #1f212a;
  /* background: linear-gradient(90deg,
      var(--sidebar-menu-bg-light) 0%,
      var(--sidebar-menu-bg-dark) 100%); */
  background-color: var(--rx-sidenav-header-bg) !important;
}

.user-type-text {
  font-size: 35px;
  font-weight: 500;
  color: var(--card-text-color);
}

.notification-section img {
  cursor: pointer;
}

.notification,
.notification-bell {
  position: relative;
  display: inline-block;
}

.notification-badge {
  position: absolute;
  top: -7px;
  right: -5px;
  background-color: red;
  color: white;
  padding: 2px 5px;
  border-radius: 50%;
  font-size: 8px;
}

.edit.notification-badge {
  font-size: 10px;
  min-width: 10px;
  height: 20px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.profile-dropdown {
  list-style: none !important;
  margin-top: 5px !important;
  /* color: #ffffff !important; */
}

.profile-dropdown a {
  color: var(--Rx-title) !important;
  margin-left: 8px !important;
  font-size: 14px !important;
}

.menu-page-title {
  color: var(--Rx-title) !important;
  font-size: 16px !important;
  font-weight: 400 !important;
}

/* end header  */

/* start metronic sidebar */
[data-kt-app-layout="dark-sidebar"] .app-sidebar {
  /* background: linear-gradient(180deg,
      var(--sidebar-menu-bg-light) 0%,
      var(--sidebar-menu-bg-dark) 100%); */
  background-color: var(--rx-sidenav-header-bg);
}

[data-kt-app-layout="dark-sidebar"] .app-sidebar .menu .menu-item .menu-link:hover span {
  color: var(--Rx-bg) !important;
}

[data-kt-app-layout="dark-sidebar"] .app-sidebar .menu .menu-item .menu-link .menu-title {
  /* color: var(--card-text-color) !important; */
  color: var(--rx-sidenav-link);
  font-size: 12px !important;
  font-weight: 400 !important;
}

[data-kt-app-layout="dark-sidebar"] .app-sidebar .menu .menu-item .menu-link.active {
  /* background: var(--sidebar-menu-bg-light) !important; */
  color: var(--Rx-bg);
  background-color: var(--rx-sidenav-link-bg);
}

[data-kt-app-layout="dark-sidebar"] .app-sidebar .menu .menu-item .menu-link.active:hover span {
  color: var(--Rx-bg) !important;
}

[data-kt-app-layout="dark-sidebar"] .app-sidebar .menu .menu-item .menu-link .menu-icon {
  color: var(--card-text-color) !important;
}

[data-kt-app-layout="dark-sidebar"] .app-sidebar .app-sidebar-logo {
  border-bottom: 1px solid #1f212a !important;
  background-color: var(--rx-sidenav-header-bg);
}

.menu-toggle {
  color: var(--card-text-color) !important;
}

.app-sidebar-logo {
  height: calc(var(--bs-app-header-height)) !important;
}

.scroll-y,
.hover-scroll-y,
.hover-scroll-overlay-y {
  overflow-y: auto;
  position: relative;
}

.app-sidebar-wrapper {
  height: calc(100vh - 215px) !important;
  overflow-y: scroll !important;
  scrollbar-width: thin;
  scrollbar-color: var(--Rx-35blue-white) transparent !important;
}

.sidebar-footer {
  background-color: var(--rx-sidenav-help);
  border-radius: 8px !important;
  margin-right: 5px;
  position: absolute;
  bottom: 0;
  width: calc(100% - 15px) !important;
  margin: 8px;
}

.sidebar-need-help {
  margin: 8px !important;
  text-align: start !important;
  padding-left: 10px !important;
}

.sidebar-footer .sidebar-need-help p {
  color: var(--rx-sidenav-footer-content) !important;
  font-weight: 400 !important;
}

.sidebar-footer-logo {
  position: absolute;
  top: -20px;
  left: 20px;
}

.title-need-help {
  color: var(--rx-sidenav-title);
  font-weight: 600 !important;
}

.side-footer-toggle-logo {
  height: 30px !important;
}

.version-popup {
  position: fixed;
  bottom: 0px;
  left: 262px;
  display: flex;
  align-items: center;
  z-index: 10;
  margin-left: 10px;
}

.version-popup-sm {
  align-items: center;
}

.version-popup-sm .reload-btn {
  padding: 0;
  margin-left: 6px;
}

.reload-btn {
  cursor: pointer;
  padding: 10px;
}

/* svg color change css */
.menu-item .menu-link.active .menu-icon svg g path {
  fill: #ffffff !important;
}

.menu-item .menu-link:hover .menu-icon svg g path {
  fill: var(--Rx-bg) !important;
}

.menu-item .menu-link:hover {
  background-color: var(--Rx-btn-bg) !important;
}

.sidebar-menu-item {
  position: relative;
  overflow: hidden;
}

.sub-dropdown-menu {
  display: block;
  position: relative;
  margin-left: 10px;
  z-index: 1;
}

.sub-dropdown-menu>.sidebar-menu-item {
  white-space: nowrap;
}

/* .sidebar-menu-item.expanded {
  padding-bottom: 10px;
} */

.dropdown-icon {
  position: absolute;
  right: 10px;
  top: 10px;
  color: var(--rx-sidenav-footer-content);
}

/* end metronic sidebar */

/* start metronic 
  
      
  /* start modal css */
.modal-header {
  display: inline-block !important;
  flex-shrink: 0;
  padding: 0.3rem 1rem;
  padding-top: 0.3rem;
  padding-right: 1rem;
  padding-bottom: 0.3rem;
  padding-left: 1rem;
  border-bottom: 1px solid var(--card-text-color);
}

.modal-content {
  padding: 20px 20px;
  background-color: var(--Rx-bg);
}

.modal-title {
  font-size: 18px;
  font-weight: 500;
}

.modal-sub-title {
  font-size: 17px;
  font-weight: 500;
  color: var(--card-text-color);
}

.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 10px;
}

.modal.fade.show {
  background: #5c5c5c78;
}

/* end modal css */

/* start switch css */
.checkbox {
  display: none;
}

.switch {
  align-items: center;
  background-color: gray;
  border-radius: 500px;
  cursor: pointer;
  display: flex;
  height: 25px;
  justify-content: space-between;
  padding: 0 10px;
  position: relative;
  user-select: none;
  width: 76px;
}

.checkbox:checked~.switch {
  background-color: #d90505;
}

.checkbox:not(:checked)~.switch {
  background-color: #008000;
}

.checkbox:checked~.w-130px,
.checkbox:checked~.access {
  background-color: #001f3f;
}

.checkbox:not(:checked)~.w-130px,
.checkbox:not(:checked)~.access {
  background-color: #3a6d8c;
}

.switch__left,
.switch__right {
  color: white;
  font-weight: bold;
  text-transform: capitalize;
}

.checkbox:checked~.switch .switch__left {
  visibility: hidden;
}

.checkbox:not(:checked)~.switch .switch__right {
  visibility: hidden;
}

.switch__circle {
  height: 40px;
  padding: 5px;
  position: absolute;
  transition: all 0.1s linear;
  width: 40px;
}

.checkbox:checked~.switch .switch__circle {
  left: 0;
  right: calc(100% - 40px);
  top: 0px;
}

.checkbox:not(:checked)~.switch .switch__circle {
  left: calc(100% - 28px);
  right: 0;
  top: 0px;
}

.switch__circle-inner {
  background-color: white;
  border-radius: 50%;
  display: block;
  width: 1.125rem;
  height: 1.125rem;
}

.switch__left,
.switch__right {
  color: white;
  font-weight: 500;
  text-transform: capitalize;
  padding-top: 3px;
  font-size: 12px;
  margin-bottom: 2px;
}

.switch__right {
  position: absolute;
  right: 8px;
}

/* end switch css */

/* start kendo css */
/* change start  */
/* .k-grid {
  border: 1px solid #d6d6d8 !important;
} */

.k-grid-toolbar {
  display: flex !important;
  justify-content: start !important;
  width: 100%;
  overflow-x: auto;
  white-space: nowrap;
  display: inline-block !important;
}

/* .k-grid .k-grid-header {
  background-color: var(--card-bg-body) !important;
}
.k-grid .k-grid-header .k-table-th {
  background-color: var(--card-bg-body) !important;
} */

:focus {
  outline: none !important;
  /* border: 1px solid !important; */
}

:focus-visible {
  outline: none !important;
}

/* .k-column-title {
  white-space: nowrap;
  color: var(--table-header) !important;
} */

/* .k-table-td {
  color: var(--table-body) !important;
  font-size: 14px;
} */

.k-table-td>a>.k-svg-icon>svg {
  fill: var(--table-body) !important;
}

.k-sort-icon>.k-icon>svg {
  fill: var(--table-body) !important;
}

/* .k-grid-content {
  overflow-y: auto;
  height: calc(100vh - 310px);
  background-color: var(--card-bg-body) !important;
} */

/* .k-grid-header {
  border-bottom: 1px solid #d6d6d8 !important;
} */

/* .k-table-th {
  border: 0px !important;
  border-bottom: 1px solid #d6d6d8 !important;
  background-color: var(--card-bg-body) !important;
  white-space: nowrap !important;
} */

/* .k-grid td {
  border: 0px !important;
  border-bottom: 1px solid #d6d6d8 !important;
  background-color: var(--card-bg-body) !important;
  white-space: nowrap !important;
} */

/* change till here */

.k-pager-nav {
  color: var(--table-body) !important;
}

.k-pager-md .k-pager-sizes .k-dropdown-list,
.k-pager-md .k-pager-sizes .k-dropdown,
.k-pager-md .k-pager-sizes .k-dropdownlist,
.k-pager-md .k-pager-sizes>select {
  /* color: var(--btn-color) !important;
          background-color: var(--btn-bg) !important; */
  color: var(--btn-dark-color) !important;
  border: 1px solid var(--Rx-35blue-white) !important;
  background-color: var(--Rx-35blue-white) !important;
}

.k-pager {
  color: var(--table-body) !important;
  background-color: var(--card-bg-body) !important;
  flex-wrap: wrap;
  border-bottom-left-radius: 6px !important;
  border-bottom-right-radius: 6px !important;
}

.k-pager-info {
  color: var(--table-body) !important;
}

.k-button:active::before,
.k-button.k-active::before {
  opacity: 0 !important;
}

.k-button-flat-primary {
  color: var(--table-body) !important;
}

.k-list-item.k-selected {
  background-color: var(--Rx-35blue-white) !important;
  color: var(--Rx-bg) !important;
}

.k-grid-pager {
  border-top: 0px !important;
}

.k-animation-container {
  z-index: 10003 !important;
}

.k-combobox.k-input.k-input-md.k-rounded-md.k-input-solid.form-control {
  background-color: var(--Rx-15-F6-color) !important;
  color: var(--Rx-title) !important;
  border: 1px solid #8c8c8c;
}

.k-combobox.k-input.k-input-md.k-rounded-md.k-input-solid.form-control:focus {
  background-color: var(--Rx-15-F6-color) !important;
  color: var(--Rx-title) !important;
  outline: none !important;
  border: 1px solid var(--Rx-FFwhite-8Cgray) !important;
  outline: 0 !important;
  box-shadow: none !important;
}

.k-combobox.k-input.k-input-md.k-rounded-md.k-input-solid.form-control:hover {
  background-color: var(--Rx-15-F6-color) !important;
  color: var(--Rx-title) !important;
  outline: none !important;
  border: 1px solid var(--Rx-35blue-white) !important;
  outline: 0 !important;
  box-shadow: none !important;
}

.k-combobox .k-input-button {
  display: none !important;
}

.k-combobox.k-input.k-input-md.k-rounded-md.k-input-solid.form-control input {
  color: var(--Rx-title) !important;
  font-size: 16px !important;
}

.k-combobox.k-input.k-input-md.k-rounded-md.k-input-solid.form-control input::placeholder {
  font-size: 16px !important;
  color: #8c8c8c !important;
}

.k-combobox>button {
  background-color: transparent !important;
}

.k-list-item:hover .custom-k-list-item {
  color: var(--btn-dark-color) !important;
  font-size: 6px !important;
}

.k-list-group-sticky-header {
  font-size: 15px !important;
  padding-left: 12px !important;
  background-color: var(--Rx-FFwhite-8Cgray) !important;
}

.k-list-group-item .k-list-item-text {
  font-size: 17px !important;
  padding-left: 2px !important;
  color: var(--rx-sidenav-link) !important;
}

.k-list-item .k-list-item-text {
  padding-left: 12px !important;
}

.k-button-solid-base {
  border-color: transparent !important;
}

.k-multiselect.k-input.form-control .k-input-inner::placeholder {
  color: #8c8c8c !important;
  font-size: 16px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 24px !important;
}

.k-multiselect.k-input.form-control:hover {
  outline: none !important;
  border: 1px solid var(--Rx-35blue-white) !important;
  outline: 0 !important;
  box-shadow: none !important;
}

.k-list-item:focus,
.k-list-optionlabel:focus,
.k-list-item.k-focus,
.k-focus.k-list-optionlabel {
  box-shadow: none !important;
}

.k-list-item:hover,
.k-list-optionlabel:hover,
.k-list-item.k-hover,
.k-hover.k-list-optionlabel {
  color: var(--btn-dark-color) !important;
  background-color: var(--Rx-35blue-white) !important;
}

.k-list-ul {
  background-color: var(--Rx-15-F6-color);
}

.k-list-item,
.k-list-optionlabel {
  color: var(--card-label-color);
}

/* .k-combobox .k-input .k-input-md .k-rounded-md .k-input-solid .form-control:focus{
          border: 1px solid #0066cd!important;
      } */
.k-combobox.k-input.k-input-md.k-rounded-md.k-input-solid.form-control:focus {
  border: 1px solid var(--Rx-FFwhite-8Cgray) !important;
}

.k-input-solid:focus-within {
  box-shadow: none !important;
}

/* td.k-table-td.k-hierarchy-cell{
          display: none !important;
      } */
.ellipsis-cell {
  /* overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          line-clamp: 1;
          -webkit-box-orient: vertical; */
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: block !important;
}

.k-tooltip {
  border-color: var(--card-text-color) !important;
  color: var(--btn-dark-color) !important;
  background-color: var(--card-text-color) !important;
  box-shadow: none !important;
}

.modal-open .k-tooltip {
  display: none !important;
}

.k-tooltip .k-callout {
  color: var(--card-text-color) !important;
}

.k-button-solid-base {
  background-image: none !important;
}

.k-multiselect {
  background-color: var(--bs-body-bg) !important;
}

.k-input-inner:disabled {
  color: var(--card-text-color) !important;
}

.k-input-md .k-input-values>.k-input-inner {
  padding: 0px;
}

.k-combobox .k-input-inner {
  padding: 0px !important;
}

.k-multiselect .k-input-values {
  /* padding: 0px !important; */
  overflow: hidden !important;
  height: 27px;
}

.k-input-solid:focus,
.k-input-solid.k-focus {
  border: 1px solid var(--Rx-btn-bg) !important;
}

.k-input-solid:hover,
.k-input-solid.k-hover {
  border: 1px solid #8c8c8c !important;
}

.k-input-solid:focus-within {
  border: 1px solid var(--Rx-btn-bg) !important;
}

.k-grid-toolbar {
  background-color: var(--card-bg-body) !important;
}

/* .k-input-solid:hover, .k-input-solid.k-hover{
          border: 1px solid #0066CD !important;
      } */
/* .k-input-md .k-input-inner, .k-picker-md .k-input-inner{
          padding: 0px !important;
      } */
/* end kendo css */

/* start subgrid css */
svg.innersvg {
  fill: #f5f5f5;
  height: 25px;
  width: 25px;
}

.icon-div {
  display: flex;
  align-items: center;
  justify-content: center;

  border-radius: 12px;
  padding: 12px;
}

.innerbox-2 {
  display: flex;
  align-items: center;
  justify-content: center;

  border-radius: 12px;
  padding: 12px;
}

.inner-card {
  border-radius: 12px !important;
  background-color: var(--table-header) !important;
}

.inner-header-1 {
  font-weight: 500;
  font-size: 15px;
  margin-bottom: 0px;
  color: var(--btn-color);
}

.inner-header-2 {
  font-weight: 500;
  font-size: 28px;
  margin-bottom: 0px;
  color: var(--btn-color);
}

.grid-inner-box {
  width: 30%;
  margin-right: 35px;
}

.innerbox-div-1 {
  width: 25%;
  /* margin-right: 34px; */
}

.innerbox-div-2 {
  width: 75%;
  text-align: center;
}

/* end subgrid css */

/* start goose chat configuration css */
.instanc-grid .k-grid-content {
  /* height: calc(100vh - 600px); */
  height: auto;
}

/* end goose chat configuration css */

/* start Chat css */

.swiper-button-prev:after,
.swiper-button-next:after {
  font-size: 16px !important;
  font-weight: 700 !important;
}

.rts___tab {
  padding: 6px 30px !important;
  color: var(--table-header);
}

.rts___tab.rts___btn.rts___tab___selected {
  background-color: var(--Rx-btn-bg) !important;
  color: var(--Rx-bg) !important;
}

.rts___nav___btn:hover {
  background-color: var(--Rx-btn-bg) !important;
  transition: 0.5s all;
}

.rts button:disabled.rts___btn svg,
.rts___nav___btn:disabled svg,
button[disabled].rts___btn svg {
  stroke: gray !important;
}

.rts___btn.rts___nav___btn {
  margin: 6px !important;
}

.rts___svg___icon {
  stroke: var(--Rx-title) !important;
  height: 20px;
  width: 20px;
}

.rts___svg___icon:hover {
  stroke: var(--Rx-bg) !important;
}

.new-chat-btn .dropdown-toggle::after,
.chat-action .dropdown-toggle::after,
.remove-bs-arrrow .dropdown-toggle::after {
  display: none !important;
}

.chat-modal .modal-content {
  padding: 10px 15px;
  background-color: var(--Rx-bg);
}

.search-group-msg-placeholder {
  height: 80vh;
  width: 480px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.search-tab .nav-line-tabs .nav-item .nav-link {
  border-radius: 100px !important;
  padding: 2px 16px !important;
  /* border: 1px solid !important; */
  margin-right: 15px !important;
  font-size: 14px;
  background: transparent !important;
  color: var(--card-label-color);
}

.selectNewChat-badge {
  background-color: var(--Rx-bg);
  color: var(--Rx-title);
  font-size: 12px;
  padding: 2px 10px;
  border-radius: 100px;
  margin-left: 8px;
}

.custom-card.selected {
  border: 1px solid #007bff;
}

.k-chat {
  background-color: #f7f9fb;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  position: relative;
}

.k-chat .k-message-list-content {
  padding-block: 8px !important;
  padding-inline: 8px !important;
}

.k-message-list {
  /* max-height: 538px; */
  overflow-y: auto;
  padding: 10px;
}

.k-timestamp {
  font-size: 12px;
  color: #9ca3af;
  text-align: center;
  /* margin: 10px 0; */
}

.k-message-group.k-alt {
  display: flex;
  align-items: flex-start;
}

.k-avatar {
  margin-right: 10px;
  /* border: 1px solid #000 !important; */
}

.k-message {
  background-color: #deeaef;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  min-width: 80px;
}

.k-message-time {
  font-size: 12px;
  color: #9ca3af;
  margin-bottom: 5px;
}

.message-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.message-content {
  font-size: 14px;
  color: #374151;
}

/* .reaction-main {
  position: absolute;
  bottom: 0%;
  left: 22px;
  display: none;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
} */

.message-actions {
  opacity: 0;
  display: none;
  transition: opacity 0.2s ease-in-out;
}

.reactions-popup {
  display: flex;
  /* gap: 10px; */
  position: absolute;
  background-color: var(--bg-message);
  border-radius: 8px;
  padding: 6px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 20px 25px -5px,
    rgba(0, 0, 0, 0.04) 0px 10px 10px -5px;
  top: -25px;
  right: -70px;
  z-index: 10;
}

/* .reactions-popup:first-child {
  top: 0px;
} */

.reactions-popup .message-action {
  transition: all ease 0.3s;
  width: 25px;
  height: 20px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  padding: 0 4px;
  /* margin-top: 2px; */
}

.reactions-popup .message-action:hover {
  scale: 1.4;
}

.chat-messsage-1 .reactions-popup {
  top: -15px;
}

.message-action .dropdown-menu {
  margin-right: 15px;
}

.self-reaction {
  right: 0px;
}

.show-action {
  position: absolute;
  left: 8px;
  bottom: -3px;
}

.show-action-div {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px #0000001a;
  padding: 2px 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background-color: var(--bg-message);
  border-radius: 8px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 20px 25px -5px,
    rgba(0, 0, 0, 0.04) 0px 10px 10px -5px;
  color: var(--Rx-title);
  margin-top: -20px;
  margin-left: 7px;
  margin-right: 5px;
}

.reaction-chip {
  z-index: 9;
}

.show-action-div svg {
  fill: var(--Rx-title);
}

.reaction-view {
  gap: 2px;
}

.msg-time {
  position: absolute;
  bottom: 0px;
  right: 4px;
  color: #9ca3af;
  font-size: 10px;
}

.k-chat .k-input-chat {
  margin: 0;
  padding: 0;
  width: 100%;
  min-width: 0;
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  outline: 0;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.4285714286;
  font-weight: normal;
  text-align: start;
  box-shadow: none;
  display: inline-flex;
  flex-flow: row nowrap;
  align-items: stretch;
  vertical-align: middle;
  overflow: hidden;
  text-overflow: ellipsis;
}

.k-chat .k-message-list {
  flex: 1 1 0 !important;
}

.send-file-dropdown {
  display: flex;
  gap: 10px;
  position: absolute;
  bottom: 30px;
  right: 0px;
  background-color: var(--send-file-wrapper-bg);
  border-radius: 8px;
  padding: 2px 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.file-send-box {
  padding: 5px;
  border: 1px solid #afb8ce;
  width: 100%;
  display: flex;
  align-items: center;
  overflow-x: auto;
}

.file-send-box:last-child {
  margin-bottom: -24px;
}

.file-details {
  position: relative;
  padding: 0px 3px;
}

.file-remove-btn {
  position: absolute;
  top: -3px;
  right: 4px;
  color: red;
  cursor: pointer;
}

.attachment-files {
  max-width: 100%;
  height: 212px;
  object-fit: fill;
  margin-bottom: 4px;
}

/* .message-dropdown {
  position: absolute;
  top: 20px;
  right: 0px;
  background-color: white;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  z-index: 10;
  padding: 8px 0;
  min-width: 120px;
  display: flex;
  flex-direction: column;
} */

/* .message-dropdown div {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  white-space: nowrap;
}

.message-dropdown div:hover {
  background-color: #f1f1f1;
} */

/* .k-chat .k-message-group.k-alt:hover .reaction-main, */
.k-only.k-message:hover .message-actions {
  opacity: 1;
  display: block;
}

.k-chat .k-message-group.k-alt {
  text-align: left !important;
}

.k-message-box {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #f3f4f6;
  border-top: 1px solid #e5e7eb;
}

.k-input-inner {
  flex: 1;
  padding: 10px;
  border: none;
  border-radius: 4px;
  outline: none;
  font-size: 14px;
  color: #374151;
  background-color: #ffffff;
}

.k-input-suffix {
  display: flex;
  align-items: center;
}

.k-icon-button {
  background-color: transparent;
  border: none;
  cursor: pointer;
}

.k-chat-toolbar {
  background-color: #f7f9fb;
  padding: 10px;
  border-top: 1px solid #e5e7eb;
}

.k-toolbar-group {
  display: flex;
  gap: 10px;
}

.reply-indicator-main {
  background-color: var(--bg-self-message);
  padding: 5px;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;
}

.reply-indicator-div {
  background-color: var(--bg-message);
  padding: 5px;
  border: 1px solid #afb8ce;
  border-radius: 10px;
  width: 90%;
  border-left: 4px solid #34b7f1;
  max-height: 70px;
  overflow: hidden;
}

.video-play-icon {
  position: absolute;
  top: 6px;
  left: 7px;
  font-size: 26px;
  color: #fff;
  cursor: pointer;
}

.replying-to {
  font-style: italic;
  color: gray;
  font-size: 12px;
}

.reply-indicator-msg {
  background-color: var(--bg-message);
  padding: 6px;
  border-left: 4px solid #34b7f1;
  margin-bottom: 5px;
  border-radius: 4px;
  min-width: 75px;
  width: 100%;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  max-height: 50px;
  overflow: hidden;
}

.reply-indicator-msg:hover {
  filter: opacity(0.9);
}

.reply-files-main-div {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.reply-file {
  background-color: #fff;
  opacity: 0.3;
  border-radius: 4px;
}

.reply-file-count {
  position: absolute;
  top: 8px;
  left: 10px;
  font-weight: 500;
}

.audio-recorder-chat .audio-recorder {
  width: auto !important;
}

.chat-modal .k-chat {
  height: calc(100vh - 65px) !important;
  background-color: var(--Chat-K);
}

.chat-conversion-input {
  width: 100%;
  height: 30px;
  border: none;
  border-radius: 4px;
  padding: 0 10px;
  background-color: var(--Rx-15-F6-color) !important;
  color: var(--Rx-title);
}

.conversion-user-card {
  max-height: 15.5em !important;
  overflow: auto !important;
}

.highlight-badge {
  border-radius: 99px;
  width: 10px;
  height: 10px;
  background-color: var(--message-text);
}

.group-creator {
  font-size: 10px;
  color: var(--Rx-bg);
  background-color: var(--Rx-title);
  border-radius: 4px;
  padding: 2px 6px;
  margin: 8px 0px 0px 0px;
  width: fit-content;
}

.edit-group-icon {
  position: absolute;
  top: 6px;
  right: 6px;
  font-size: 18px;
  cursor: pointer;
}

/* end Chat css */

/* start goose chat css */

.chat-container .k-chat {
  min-width: 100% !important;
}

.goose-chat {
  display: flex;
  /* height: 86vh; */
  max-height: calc(100vh - 110px);
  color: white;
}

.goose-chat .sidebar-wrapper {
  padding: 10px 0px 0px 10px !important;
}

.custom-sidebar {
  color: white;
  width: 250px;
  min-width: 250px;
}

.css-1dabzo2 {
  z-index: 10;
  height: calc(100vh - 142px);
  border: 1px solid var(--btn-dark-bg) !important;
  border-radius: 4px !important;
  /* padding: 10px 0px 0px 10px !important; */
}

.css-1dabzo2.ps-collapsed {
  width: 65px !important;
  min-width: 65px !important;
}

.logo-section {
  padding: 10px;
  text-align: center;
  display: flex;
  justify-content: space-between;
}

.ps-sidebar-container.css-dip3t8 {
  overflow-y: hidden !important;
  background-color: var(--Chat-K);
}

.menu-wrapper {
  padding: 10px;
  overflow-y: auto;
  height: calc(100vh - 164px);
}

.history-date {
  font-size: 14px !important;
  margin-bottom: 5px;
  color: #626161;
}

.history-text {
  font-size: 14px;
  color: #222222;
  background-color: #e5e3e3;
  padding: 6px 10px;
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 10px 0px 0px 10px;
}

.k-chat-message-box {
  width: 100%;
  height: auto;
  flex: none;
  flex-flow: row nowrap;
  align-items: center;
}

.goose-chat .k-message {
  padding: 0;
  background-color: transparent;
  box-shadow: none;
}

.k-message-sent .k-message-content {
  background-color: var(--Send-msg);
  color: #fff;
  border-radius: 8px;
}

.k-message-received .k-message-content {
  background-color: #ececec;
  /* color: var(--Rx-title); */
  border-radius: 8px;
}

.k-message-sent .k-message-text p {
  margin: 0;
}

.k-message-text h3 {
  color: var(--goosechat-question-) !important;
}

.k-chat {
  height: calc(100vh - 144px) !important;
  background-color: var(--Chat-K) !important;
  border: 1px solid var(--btn-dark-bg) !important;
  border-radius: 4px !important;
}

.k-chat.no-border {
  border: 0px solid var(--btn-dark-bg) !important;
  background-color: transparent !important;
}

.mention {
  background-color: #d3e1eb;
  color: #000000 !important;
}

.ticketPageChat .k-chat,
.ticketPageChat {
  max-height: calc(100vh - 180px) !important;
  height: 100%;
}

.inspectionPageChat .k-chat,
.inspectionPageChat {
  max-height: calc(100vh - 300px) !important;
  height: 100%;
}

.k-chat-100 {
  max-width: 100vw !important;
}

.k-chat.collapsed-false {
  max-width: calc(100vw - 72vw) !important;
}

.k-chat.collapsed-true {
  max-width: calc(100vw - 59vw) !important;
}

.k-chat .k-message-group.k-alt {
  text-align: start !important;
}

.k-chat .k-timestamp {
  display: none;
}

.k-only.k-message .k-message-time {
  display: none;
}

.k-chat .k-message.k-selected {
  margin-bottom: 0 !important;
}

.k-avatar-solid-primary {
  background-color: transparent !important;
}

.header-chat {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.k-message-actions {
  display: flex;
  align-items: center;
  justify-content: end;
  gap: 8px;
  margin-top: 5px;
}

.k-message-actions .k-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  font-size: 20px;
}

.k-message-actions .k-button:hover {
  color: #007bff;
}

.modal-right-halfs .modal-dialog {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 50%;
  max-width: 100%;
  margin: 0;
  -webkit-transform: translate(100%, 0) !important;
  transform: translate(100%, 0) !important;
  -webkit-transition: 0.5s;
  transition: 0.5s;
  height: 100%;
}

.modal-right-halfs.show .modal-dialog {
  -webkit-transform: translate(0, 0) !important;
  transform: translate(0, 0) !important;
}

.modal-right-halfs .modal-content {
  height: 100%;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  flex-direction: column;
  border-radius: 0;
}

.group-action {
  position: relative;
  display: none;
  font-size: 16px;
  color: gray;
}

.history-text.active .group-action {
  display: block;
}

.group-action-dropdown {
  display: none;
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #fff;
  border: 1px solid #adacac;
  border-radius: 4px;
}

.group-action:hover .group-action-dropdown {
  display: block;
}

.delete-grouplist,
.edit-grouplist {
  display: flex;
  align-items: center;
  padding: 6px;
}

.delete-grouplist:hover,
.edit-grouplist:hover {
  background-color: #f0f0f0;
  border-radius: 4px;
}

.history-text.active {
  background-color: #fff;
  box-shadow: 0 0 2px var(--btn-dark-bg);
}

.like-active {
  color: #007f00;
}

.dislike-active {
  color: #ff0000;
}

.feedback-options {
  border: 1px solid #ccc;
  border-radius: 6px;
  margin-top: 6px;
}

.popover-message {
  position: absolute;
  top: -30px;
  right: 0;
  background-color: #333;
  color: white;
  font-size: 12px;
  padding: 5px 10px;
  border-radius: 5px;
  z-index: 1000;
  opacity: 0.9;
}

.dislike-option {
  cursor: pointer;
  padding: 2px 8px;
  margin-bottom: 4px;
  margin-right: 6px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.goose-mistakes-text {
  font-size: 12px;
  color: #7d7d7d;
  margin-top: 5px;
  text-align: center;
}

.group-name {
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
}

.goose-chat-link {
  /* border: 1px solid #b2b2b2; */
  width: 100%;
  padding: 3px 8px;
  margin-bottom: 10px;
  margin-top: 2px;
  border-radius: 6px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  /* background-color: #fff; */
}

/* .goose-chat-link:hover {
  color: #489ffc;
  background-color: #fff;
} */

.k-chat-message-box ol {
  padding-inline-start: 12px !important;
}

.k-chat-message-box pre code {
  color: #392aac;
  font-size: 16px;
}

.k-avatar-md {
  width: 22px !important;
  height: 22px !important;
  flex-basis: 32px;
}

.k-chat .k-avatars .k-message-group.k-alt:not(.k-no-avatar) {
  padding-inline-end: 26px !important;
}

.k-chat .k-avatars .k-message-group:not(.k-no-avatar) {
  padding-inline-start: 26px !important;
}

.goose-send-msg-loader {
  position: relative;
  bottom: 50px;
  left: 10px;
  display: flex;
  align-items: baseline;
  color: var(--Rx-title);
}

.goose-send-msg-loader .spinner-grow {
  color: var(--Rx-btn-bg);
}

.k-chat-message-box .k-message-text.quick p {
  margin-bottom: 0px !important;
}

.k-quick-replies {
  padding-inline-start: 26px !important
}

.k-quick-replies .k-quick-reply {
  border: 1px solid var(--Rx-btn-bg) !important;
  color: var(--Rx-title) !important;
}

.k-chat .k-quick-reply:hover {
  background-color: var(--Rx-btn-bg) !important;
  color: var(--Rx-bg) !important;
}

/* end goose chat css */

/* start notification css */
.notification-delele,
.notification-read {
  cursor: pointer;
  border-bottom: 1px solid var(--Rx-title);
}

.notification-modal-body {
  padding: 1.09rem !important;
}

/* end notification css */

/* Start Hot Work Permit css */
.p-inputtext {
  border-radius: 6px !important;
  height: 45px !important;
  /* font-size: 13px !important; */
  font-weight: 500 !important;
  /* color: var(--card-text-color) !important; */
  /* border: 1px solid #999999 !important; */
  /* padding: 0.775rem 1rem !important; */
  /* height: 44px !important; */
  color: var(--Rx-title) !important;
  background-color: var(--Rx-15-F6-color) !important;
  padding: 10px 14px !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  margin-top: 5px !important;
  border: 0.5px solid #8c8c8c !important;
}

.p-inputtext::placeholder {
  color: #8c8c8c !important;
  font-size: 16px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 24px !important;
}

.p-inputtext:enabled:focus {
  outline: 0 none;
  outline-offset: 0;
  box-shadow: none !important;
  border: 1px solid var(--Rx-35blue-white) !important;
}

.p-calendar:not(.p-calendar-disabled).p-focus>.p-inputtext {
  outline: 0 none;
  outline-offset: 0;
  box-shadow: none !important;
  border: 1px solid var(--Rx-35blue-white) !important;
}

.p-datepicker.p-component.p-ripple-disabled.p-connected-overlay-enter-done {
  z-index: 9999 !important;
  min-width: 300px !important;
}

.p-datepicker table th {
  padding: 0.5rem;
  padding-left: 18px;
}

.p-datepicker.p-component {
  background-color: var(--Rx-15black-ECwhite) !important;
  border: none !important;
}

.p-datepicker-header {
  background-color: var(--Rx-15black-ECwhite) !important;
  border: none !important;
}

.p-datepicker-header button,
.p-datepicker-header .p-datepicker-title {
  color: var(--Rx-title) !important;
}

.p-datepicker-header button:hover,
.p-datepicker-header .p-datepicker-title:hover {
  color: var(--Rx-title) !important;
  background-color: var(--Rx-15black-ECwhite) !important;
}

.p-datepicker-calendar tbody tr td {
  overflow: hidden !important;
  padding: 3px !important;
  color: var(--Rx-title) !important;
  border-radius: 5px !important;
}

.p-datepicker-calendar tbody tr .p-datepicker-today span {
  border-radius: 5px !important;
  background-color: var(--Gray-1) !important;
  color: var(--Rx-bg) !important;
}

.p-datepicker-calendar tbody tr .p-highlight {
  border-radius: 5px !important;
  background-color: var(--Rx-title) !important;
  color: var(--Rx-bg) !important;
}

.p-datepicker-calendar tbody tr td span:hover {
  border-radius: 5px !important;
  background-color: var(--Rx-btn-bg) !important;
  color: var(--Rx-bg) !important;
}

.p-datepicker-calendar tbody tr td span {
  border-radius: 5px !important;
}

.p-datepicker-calendar thead tr th {
  padding: 10px !important;
}

.p-hour-picker button,
.p-minute-picker button {
  color: var(--Rx-btn-bg) !important;
}

.p-hour-picker span,
.p-minute-picker span {
  color: var(--Rx-title) !important;
}

.p-hour-picker button:hover,
.p-minute-picker button:hover {
  color: var(--Rx-bg) !important;
  background-color: var(--Rx-btn-bg) !important;
}

/* .validUntilModal {
  top: 40% !important;
  left: 70% !important;
} */

.timeModal {
  top: 20% !important;
  left: 70% !important;
}

.timeModal.modal.fade.show,
.validUntilModal.modal.fade.show {
  background: none !important;
}

.p-datepicker-timeonly {
  width: 200px !important;
  margin-bottom: 16px;
}

.form-title {
  color: var(--Rx-title);
  font-weight: 600;
  font-size: 15px;
}

.red-grid-addnew-template {
  height: calc(100vh - 232px);
  overflow-y: scroll;
}

.company-select {
  border-radius: 0px !important;
  padding: 5px !important;
  overflow: auto;
  height: 130px !important;
  scrollbar-width: thin;
  scrollbar-color: var(--Rx-35blue-white) transparent !important;
  background-color: transparent !important;
  border: none !important;
  width: 100 !important;
}

.company-select option {
  padding: 10px !important;
  border-bottom: 1px solid gray !important;
}

.add-new-company-btn {
  background-color: transparent !important;
  color: var(--Rx-title) !important;
  border: none !important;
  margin-top: 2px !important;
  margin-bottom: 5px !important;
}

.dynamicDropdown-section {
  background-color: var(--Rx-15-F6-color) !important;
  border-radius: 5px !important;
  margin-left: 6px !important;
}

.order-list-style {
  list-style: upper-alpha;
}

.signature-input {
  /* border: 2px solid !important; */
  height: 90px !important;
  border-style: dashed !important;
}

.upload-btn-hot {
  cursor: pointer;
  position: absolute;
  top: 28px;
  left: 250px;
}

.add-icon-hot {
  height: 22px;
  width: 22px;
  background-color: #fff;
  color: #030303;
  border-radius: 50%;
}

.popover-body {
  position: absolute;
  top: 43px;
  left: 60px;
  background-color: var(--Rx-bg) !important;
  border-radius: 15px !important;
}

.popover-section {
  background-color: var(--Rx-bg) !important;
}

.popover-arrow {
  display: none !important;
}

.tabscroll-list {
  height: calc(100vh - 373px);
  overflow: auto;
}

.notification {
  position: relative;
}

.tooltip-text {
  visibility: hidden;
  background-color: var(--card-bg-body);
  color: var(--Rx-title);
  text-align: center;
  padding: 10px 5px;
  border-radius: 5px;
  position: absolute;
  z-index: 1;
  top: 20%;
  right: 100%;
  /* transform: translateX(-80%); */
  width: 450px;
  opacity: 0;
  transition: opacity 0.3s;
}

.notification:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}


.warning-dropdown .k-button-solid-base.k-icon-button.k-input-button {
  position: absolute;
  right: 0px;
}

.hot-datepicker .k-input-solid {
  background-color: transparent !important;
}

.hot-datepicker .k-input-solid:hover,
.k-input-solid.k-hover {
  border: none !important;
}

.hot-datepicker .k-input.k-datepicker {
  border-radius: 6px !important;
  height: 45px !important;
  font-weight: 500 !important;
  color: var(--Rx-title) !important;
  background-color: var(--Rx-15-F6-color) !important;
  padding: 10px 14px !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  margin-top: 5px !important;
  border: 0.5px solid #8c8c8c !important;
}

.hot-datepicker .k-input.k-datepicker:hover {
  border: 1px solid var(--Rx-35blue-white) !important;
  outline: 0 !important;
  box-shadow: none !important;
}

.hot-datepicker .k-button-solid-base {
  background-color: var(--btn-color) !important;
}

.k-popup {
  background-color: transparent !important;
}

/* end Hot Work Permit css */

/* start Vendor loop css */
.new-associate {
  height: calc(100vh - 426px);
  overflow-y: auto;
  /* border-top: 1px solid gray; */
  margin-top: 16px;
  position: relative;
}

/* .associate-search {
  position: sticky;
  top: 0px;
  left: 0px;
  z-index: 1;
} */

.k-panelbar.k-accordion.loop-accordion {
  z-index: 0 !important;
}

/* end Vendor loop css */

/* start During Red Tag css */
.Check-pending {
  background-color: #5E0E11;
  border-radius: 40px;
}

.check-success {
  background-color: #0E3E24;
  border-radius: 40px;
}

.sorting-member {
  position: absolute;
  top: 110px;
}

.comment-section-checkins {
  height: calc(100vh - 300px) !important;
  overflow-y: auto;
}

.checkins-popup {
  width: calc(100vw - 800px) !important;
  padding: 10px 20px !important;
  border-radius: 20px !important;
  background-color: var(--Rx-bg) !important;
}


.cutom-title-checkins-popup {
  display: flex !important;
}

.menu-bullet {
  display: none !important;
}

.inspection {
  height: calc(100vh - 190px);
  overflow-y: scroll !important;
}

.inspection-content-conditionn>span>input[type="checkbox"] {
  /* Remove the default appearance of the checkbox */
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 20px;
  height: 20px;
  /* border: 2px solid #ccc; */
  border-radius: 4px;
  background-color: #49C481;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.inspection-content-conditionn>span>input[type="checkbox"]:checked {
  background-color: red;
  /* border-color: green; */
  border: 4px solid gold;
  position: relative;
}

.inspection-content-conditionn>span>input[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  left: 6px;
  top: 3px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* end During Red Tag css */

/* Quil Ck-Editor CSS Start */
.ql-snow.ql-toolbar button:hover .ql-stroke,
.ql-snow .ql-toolbar button:hover .ql-stroke,
.ql-snow.ql-toolbar button:focus .ql-stroke,
.ql-snow .ql-toolbar button:focus .ql-stroke,
.ql-snow.ql-toolbar button.ql-active .ql-stroke,
.ql-snow .ql-toolbar button.ql-active .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-snow.ql-toolbar button:hover .ql-stroke-miter,
.ql-snow .ql-toolbar button:hover .ql-stroke-miter,
.ql-snow.ql-toolbar button:focus .ql-stroke-miter,
.ql-snow .ql-toolbar button:focus .ql-stroke-miter,
.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,
.ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter {
  stroke: var(--color-text);
}

.ql-snow.ql-toolbar button:hover,
.ql-snow .ql-toolbar button:hover,
.ql-snow.ql-toolbar button:focus,
.ql-snow .ql-toolbar button:focus,
.ql-snow.ql-toolbar button.ql-active,
.ql-snow .ql-toolbar button.ql-active,
.ql-snow.ql-toolbar .ql-picker-label:hover,
.ql-snow .ql-toolbar .ql-picker-label:hover,
.ql-snow.ql-toolbar .ql-picker-label.ql-active,
.ql-snow .ql-toolbar .ql-picker-label.ql-active,
.ql-snow.ql-toolbar .ql-picker-item:hover,
.ql-snow .ql-toolbar .ql-picker-item:hover,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected {
  color: var(--color-text);
}

.ql-snow.ql-toolbar button:hover .ql-fill,
.ql-snow .ql-toolbar button:hover .ql-fill,
.ql-snow.ql-toolbar button:focus .ql-fill,
.ql-snow .ql-toolbar button:focus .ql-fill,
.ql-snow.ql-toolbar button.ql-active .ql-fill,
.ql-snow .ql-toolbar button.ql-active .ql-fill,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {
  fill: var(--color-text);
}

.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  display: block;
  margin-top: 0px;
  top: 100%;
  left: 0px;
  z-index: 1;
}

.ql-snow .ql-picker.ql-header {
  border: 1px solid var(--color-text);
  border-radius: 5px;
}

.ql-snow .ql-fill,
.ql-snow .ql-stroke.ql-fill {
  fill: var(--color-text);
}

.ql-snow .ql-stroke {
  fill: transparent;
  stroke: var(--color-text);
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-width: 2;
}

.ql-snow .ql-picker-options {
  background-color: var(--ck-editor-header-collapse);
  border-radius: 5px;

}

.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: 'Normal';
  color: var(--color-text);
}

.ql-toolbar {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  background-color: var(--ql-header-bg);
}

.ql-container {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  background-color: var(--ql-body-bg);
}

.ql-editor {
  height: calc(100vh - 400px) !important;
  overflow-y: auto !important;
}

.ql-snow .ql-tooltip {
  position: absolute;
  top: -10px !important;
  transform: translateY(10px);
  left: 230px !important;
  background-color: var(--ck-editor-body);
  border-radius: 5px !important;
  border: 1px solid var(--active_menu-bg);
  box-shadow: none !important;
  color: var(--active_menu-color);
  font-weight: 600;
}

.ql-action {
  color: var(--color-text) !important;
  font-weight: 600;
}

.ql-snow .ql-picker-options .ql-picker-item {
  padding-bottom: 3px !important;
  padding-top: 3px !important;
}

.ql-toolbar.ql-snow .ql-formats:last-child {
  margin-right: 0 !important;
}

/* Quil Ck-Editor CSS End */

/* start other css */

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  /* border: 1px solid transparent !important; */
  -webkit-text-fill-color: var(--Rx-title);
  -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

.settingSlider svg g path {
  fill: var(--btn-bg);
}

.ticket-tooltip {
  z-index: 1 !important;
}

.tooltip-inner {
  background-color: var(--Rx-15-F6-color) !important;
}

.ticket-tooltip svg g path,
.ticket-tooltip svg path,
.user-image svg g path,
.svgicon path,
.svgicon g path {
  fill: var(--btn-bg) !important;
}

.svgicon-withstrock g path {
  stroke: var(--btn-bg) !important;
}

.ticket-tooltip span {
  color: var(--btn-bg) !important;
}

.form-control {
  border-radius: 6px !important;
  height: 45px !important;
  /* font-size: 13px !important; */
  font-weight: 500 !important;
  /* color: var(--card-text-color) !important; */
  /* border: 1px solid #999999 !important; */
  /* padding: 0.775rem 1rem !important; */
  /* height: 44px !important; */
  color: var(--Rx-title) !important;
  background-color: var(--Rx-15-F6-color) !important;
  padding: 10px 14px !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  margin-top: 5px !important;
  border: 0.5px solid #8c8c8c !important;
}

.form-control::placeholder {
  color: #8c8c8c !important;
  font-size: 16px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 24px !important;
}

.textarea-form-control {
  border-radius: 6px !important;
  font-weight: 500 !important;
  color: var(--Rx-title) !important;
  background-color: var(--Rx-15-F6-color) !important;
  padding: 10px 14px !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  margin-top: 5px !important;
  border: 0.5px solid #8c8c8c;
  transition: border-color 0.3s ease;
  /* Smooth transition for border color */
}

.textarea-form-control .comment-textarea {
  width: 100% !important;
  background-color: transparent !important;
  color: var(--Rx-title) !important;
  border: none !important;
  scrollbar-width: none !important;
  font-size: 12px;
  resize: none;
  max-height: 36px !important;
  min-height: 30px !important;
}

.textarea-form-control .comment-textarea {
  width: 100% !important;
  background-color: transparent !important;
  color: var(--Rx-title) !important;
  border: none !important;
  scrollbar-width: none !important;
  font-size: 12px;
  resize: none;
  max-height: 36px !important;
  min-height: 30px !important;
}

.textarea-form-control .large-textarea {
  width: 100% !important;
  background-color: transparent !important;
  color: var(--Rx-title) !important;
  border: none !important;
  scrollbar-width: thin !important;
  font-size: 16px !important;
  resize: none;
  max-height: 88px !important;
  min-height: 80px !important;
}

.textarea-form-control .large-textarea::placeholder {
  color: #8c8c8c !important;
  font-size: 16px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 24px !important;
}

.form-label {
  /* color: var(--card-label-color); */
  color: var(--Rx-title) !important;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: 500 !important;
  line-height: 20px !important;
}

.inner-content-wrapper {
  min-height: calc(100vh - 203px) !important;
  position: relative !important;
}

.save-continue-footer {
  box-shadow: rgba(0, 0, 0, 0.15) 0px 5px 15px 0px;
  position: sticky;
  bottom: 0;
  /* right: 0; */
  /* left: 0; */
  /* z-index: 9999; */
  margin: 1px -30px 0px -30px;
}

.page-title {
  font-size: 25px;
  font-weight: 500;
}

.search-box {
  width: 200px;
  background: var(--sidebar-menu-bg-light);
  color: var(--card-text-color);
  display: inline-block !important;
  height: 35.5px !important;
}

.search-box::placeholder {
  color: var(--card-text-color) !important;
}

.search-box:focus {
  background: var(--sidebar-menu-bg-light) !important;
  color: var(--card-text-color) !important;

  border: 1px solid var(--btn-dark-bg) !important;
}

.search-box:focus::placeholder {
  color: var(--btn-dark-bg) !important;
}

.btn-dark {
  border-radius: 12px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  padding: 7px 15px !important;
  color: var(--btn-dark-color) !important;
  border: 1px solid var(--btn-dark-bg) !important;
  background-color: var(--btn-dark-bg) !important;
}

.btn-check:checked+[data-bs-theme="dark"] .btn.btn-dark,
.btn-check:active+[data-bs-theme="dark"] .btn.btn-dark,
[data-bs-theme="dark"] .btn.btn-dark:focus:not(.btn-active),
[data-bs-theme="dark"] .btn.btn-dark:hover:not(.btn-active),
[data-bs-theme="dark"] .btn.btn-dark:active:not(.btn-active),
[data-bs-theme="dark"] .btn.btn-dark.active,
[data-bs-theme="dark"] .btn.btn-dark.show,
.show>[data-bs-theme="dark"] .btn.btn-dark {
  background-color: unset !important;
}

.btn-check:checked+.btn.btn-dark,
.btn-check:active+.btn.btn-dark,
.btn.btn-dark:focus:not(.btn-active),
.btn.btn-dark:hover:not(.btn-active),
.btn.btn-dark:active:not(.btn-active),
.btn.btn-dark.active,
.btn.btn-dark.show,
.show>.btn.btn-dark {
  color: var(--btn-dark-bg) !important;
  background-color: var(--btn-dark-color) !important;
  border: 1px solid var(--btn-dark-bg) !important;
}

/* [data-bs-theme=dark] .btn.btn-dark {
          color: #0D0E12 !important;
          border: 1px solid #0D0E12 !important;
          background-color: #F5F5F5 !important;
      }
      [data-bs-theme=dark] .search-box {
          width: 200px;
          background: #F5F5F5;
          color: #0D0E12;
          display: inline-block !important;
          height: 35.5px !important;
      }
      
      } */
.btn-icon-custom {
  font-size: 18px !important;
  margin-right: 5px !important;
}

.k-pager-numbers .k-selected {
  color: var(--btn-dark-color) !important;
  border: 1px solid var(--Rx-35blue-white) !important;
  background-color: var(--Rx-35blue-white) !important;
}

.k-button.k-selected::before {
  opacity: 0.1 !important;
}

.k-button-flat:focus::after,
.k-button-flat.k-focus::after {
  opacity: 0 !important;
}

.k-button-flat-primary {
  color: #0d0e12;
}

textarea.form-control {
  min-height: calc(1.5em + 1.55rem + 40px);
}

.checkbox-span {
  font-size: 11px;
  margin-left: 5px;
  font-weight: 500;
  color: var(--card-label-color);
}

.form-check-input:checked {
  background-color: var(--btn-dark-bg);
  border: 1px solid var(--btn-dark-bg) !important;
}

.form-check-input:not(:checked) {
  border: 1px solid var(--card-label-color) !important;
}

.form-check-input {
  width: 1.55rem;
  height: 1.55rem;
  border: 1px solid var(--card-label-color) !important;
}

.form-check-input[type="checkbox"] {
  cursor: pointer;
}

.form-check-input[type="radio"] {
  cursor: pointer;
}

.form-check-custom {
  font-size: 11px;
}

.menu-sub-dropdown>.menu-item .menu-link {
  padding: 0.7rem 1rem;
  position: relative;
  font-size: 14px;
  color: var(--Rx-title) !important;
  font-weight: 500;
}

.menu-sub-dropdown>.menu-item a.menu-link:hover {
  background-color: transparent;
  color: var(--Rx-bg) !important;
}

.menu-item .menu-link:hover .menu-icon .fill-sidebar-icon,
.menu-item .menu-link.active:hover .menu-icon .fill-sidebar-icon {
  fill: var(--Rx-bg) !important;
}

.menu-item .menu-link.active .menu-icon .fill-sidebar-icon {
  fill: #ffffff !important;
}

.fill-sidebar-icon {
  /* fill: var(--rx-sidenav-footer-content); */
  fill: #7D7D7D;
}

.btn-check:checked+.btn.btn-active-light-primary,
.btn-check:active+.btn.btn-active-light-primary,
.btn.btn-active-light-primary:focus:not(.btn-active),
.btn.btn-active-light-primary:hover:not(.btn-active),
.btn.btn-active-light-primary:active:not(.btn-active),
.btn.btn-active-light-primary.active,
.btn.btn-active-light-primary.show,
.show>.btn.btn-active-light-primary {
  background-color: transparent !important;
}

.btn-check:checked+.btn.btn-active-light-primary,
.btn-check:active+.btn.btn-active-light-primary,
.btn.btn-active-light-primary:focus:not(.btn-active),
.btn.btn-active-light-primary:hover:not(.btn-active),
.btn.btn-active-light-primary:active:not(.btn-active),
.btn.btn-active-light-primary.active,
.btn.btn-active-light-primary.show,
.show>.btn.btn-active-light-primary {
  color: #f5f5f5;
  border-color: #f5f5f5;
  background-color: #f5f5f5 !important;
}

.btn-check:checked+.btn.btn-active-light-primary i,
.btn-check:checked+.btn.btn-active-light-primary .svg-icon,
.btn-check:active+.btn.btn-active-light-primary i,
.btn-check:active+.btn.btn-active-light-primary .svg-icon,
.btn.btn-active-light-primary:focus:not(.btn-active) i,
.btn.btn-active-light-primary:focus:not(.btn-active) .svg-icon,
.btn.btn-active-light-primary:hover:not(.btn-active) i,
.btn.btn-active-light-primary:hover:not(.btn-active) .svg-icon,
.btn.btn-active-light-primary:active:not(.btn-active) i,
.btn.btn-active-light-primary:active:not(.btn-active) .svg-icon,
.btn.btn-active-light-primary.active i,
.btn.btn-active-light-primary.active .svg-icon,
.btn.btn-active-light-primary.show i,
.btn.btn-active-light-primary.show .svg-icon,
.show>.btn.btn-active-light-primary i,
.show>.btn.btn-active-light-primary .svg-icon {
  color: #0d0e12 !important;
}

.btn-check:checked+.btn.btn-active-color-primary i,
.btn-check:checked+.btn.btn-active-color-primary .svg-icon,
.btn-check:active+.btn.btn-active-color-primary i,
.btn-check:active+.btn.btn-active-color-primary .svg-icon,
.btn.btn-active-color-primary:focus:not(.btn-active) i,
.btn.btn-active-color-primary:focus:not(.btn-active) .svg-icon,
.btn.btn-active-color-primary:hover:not(.btn-active) i,
.btn.btn-active-color-primary:hover:not(.btn-active) .svg-icon,
.btn.btn-active-color-primary:active:not(.btn-active) i,
.btn.btn-active-color-primary:active:not(.btn-active) .svg-icon,
.btn.btn-active-color-primary.active i,
.btn.btn-active-color-primary.active .svg-icon,
.btn.btn-active-color-primary.show i,
.btn.btn-active-color-primary.show .svg-icon,
.show>.btn.btn-active-color-primary i,
.show>.btn.btn-active-color-primary .svg-icon {
  color: #0d0e12 !important;
}

.btn.btn-color-muted i,
.btn.btn-color-muted .svg-icon {
  color: #0d0e12 !important;
}

.menu-active-bg .menu-item .menu-link.active {
  background-color: transparent;
}

.menu-state-primary .menu-item .menu-link.active .menu-icon,
.menu-state-primary .menu-item .menu-link.active .menu-icon .svg-icon,
.menu-state-primary .menu-item .menu-link.active .menu-icon i {
  color: #0d0e12;
}

.menu-state-primary .menu-item .menu-link.active .menu-title {
  color: #0d0e12;
}

.menu-state-primary .menu-item.hover:not(.here)>.menu-link:not(.disabled):not(.active):not(.here) .menu-icon,
.menu-state-primary .menu-item.hover:not(.here)>.menu-link:not(.disabled):not(.active):not(.here) .menu-icon .svg-icon,
.menu-state-primary .menu-item.hover:not(.here)>.menu-link:not(.disabled):not(.active):not(.here) .menu-icon i,
.menu-state-primary .menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-icon,
.menu-state-primary .menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-icon .svg-icon,
.menu-state-primary .menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-icon i {
  color: #0d0e12;
}

.menu-state-primary .menu-item.hover:not(.here)>.menu-link:not(.disabled):not(.active):not(.here) .menu-title,
.menu-state-primary .menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-title {
  color: #0d0e12;
}

.menu-sub-dropdown {
  border-radius: 4px;
  background-color: var(--Rx-15black-ECwhite) !important;
  z-index: 105;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.25), 0 4px 4px rgba(0, 0, 0, 0.11);
  border: 1px solid rgba(0, 0, 0, 0.15);
}

.dropdown-menu {
  border-radius: 4px;
  background-color: var(--Rx-15black-ECwhite) !important;
  z-index: 105;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.25), 0 4px 4px rgba(0, 0, 0, 0.11);
  border: 1px solid rgba(0, 0, 0, 0.15);
}

.dropdown-menu .dropdown-item:hover {
  background-color: var(--Rx-btn-bg) !important;
  color: var(--Rx-bg) !important;
}

.menu-content>div>div>span {
  color: #585858 !important;
}

.menu-state-bg .menu-item.hover:not(.here)>.menu-link:not(.disabled):not(.active):not(.here),
.menu-state-bg .menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) {
  background-color: transparent !important;
}

.show.menu-dropdown>.menu-sub-dropdown,
.menu-sub-dropdown.menu.show,
.menu-sub-dropdown.show[data-popper-placement] {
  display: flex;
  will-change: transform;
  animation: menu-sub-dropdown-animation-fade-in 0.3s ease 1,
    menu-sub-dropdown-animation-move-up 0.3s ease 1;
  background-color: var(--bs-menu-dropdown-bg-color);
}

.welcome-text-page {
  font-size: 40px !important;
  /* font-weight: 500 !important;
          line-height: 52.75px !important;
          position: absolute;
          left: 48%;
          top: 50%; */
}

.image-input-wrapper {
  border-radius: 0px;
  width: 130px;
  height: 130px;
  border: 3px solid rgb(0 0 0 / 8%);
  box-shadow: none;
  background-repeat: no-repeat;
  background-size: cover;
}

.image-wrapper {
  border-radius: 50%;
  width: 100px;
  height: 100px;
  border: 3px solid #cfcfcf;
  box-shadow: none;
  background-repeat: no-repeat;
  background-size: cover;
}

.profile_img {
  border-radius: 50%;
  width: 169px;
  height: 169px;
  border: 1px solid gray;
  box-shadow: none;
  background-repeat: no-repeat;
  background-size: cover;
}

.user_img {
  border-radius: 50%;
  width: 30px;
  height: 30px;
  border: 1px solid gray;
  box-shadow: none;
  background-repeat: no-repeat;
  background-size: cover;
  margin-top: 17px;
}

::placeholder {
  font-size: 12px;
  color: var(--plceholder-color) !important;
}

.mobile-logo-div {
  display: none !important;
}

.otp-box-margin {
  margin-right: 12px;
}

h6,
.h6,
h5,
.h5,
h4,
.h4,
h3,
.h3,
h2,
.h2,
h1,
.h1 {
  color: var(--card-text-color);
  font-weight: 500 !important;
}

.k-combobox .k-input-inner::placeholder {
  font-size: 12px;
  color: var(--plceholder-color);
}

.center-header {
  justify-content: center;
}

.td-icon {
  color: var(--table-body) !important;
  font-size: 20px;
  fill: var(--table-body) !important;
}

.app-sidebar-menu .menu>.menu-item>.menu-link>.menu-title {
  font-weight: 500;
}

.app-sidebar-logo-default {
  height: 40px !important;
}

.app-sidebar-toggle {
  background-color: #ffffff !important;
}

.id_style {
  font-weight: 600 !important;
  text-decoration: underline !important;
  cursor: pointer !important;
}

.app-navbar-item.d-lg-none.ms-2.me-n3 {
  display: none;
}

.swal2-styled.swal2-default-outline:focus {
  box-shadow: none !important;
}

.modal-right .modal-dialog {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 600px;
  max-width: 100%;
  margin: 0;
  -webkit-transform: translate(100%, 0) !important;
  transform: translate(100%, 0) !important;
  -webkit-transition: 0.5s;
  transition: 0.5s;
  height: 100%;
}

.modal-right-small .modal-dialog {
  width: 490px;
}

.modal-right.show .modal-dialog {
  -webkit-transform: translate(0, 0) !important;
  transform: translate(0, 0) !important;
}

.modal-right .modal-content {
  height: 100%;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  flex-direction: column;
  border-radius: 0;
}

.modal-right-full .modal-dialog {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  max-width: 100%;
  margin: 0;
  -webkit-transform: translate(100%, 0) !important;
  transform: translate(100%, 0) !important;
  -webkit-transition: 0.5s;
  transition: 0.5s;
  height: 100%;
}

.modal-right-full.show .modal-dialog {
  -webkit-transform: translate(0, 0) !important;
  transform: translate(0, 0) !important;
}

.modal-right-full .modal-content {
  height: 100%;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  flex-direction: column;
  border-radius: 0;
}

.modal-right-half .modal-dialog {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  /* width: 50%; */
  width: 28%;
  max-width: 100%;
  margin: 0;
  -webkit-transform: translate(100%, 0) !important;
  transform: translate(100%, 0) !important;
  -webkit-transition: 0.5s;
  transition: 0.5s;
  height: 100%;
}

.modal-right-half.show .modal-dialog {
  -webkit-transform: translate(0, 0) !important;
  transform: translate(0, 0) !important;
}

.modal-right-half .modal-content {
  height: 100%;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  flex-direction: column;
  border-radius: 0;
}

.pw_hide_show_page {
  font-size: 13px !important;
  font-weight: 500 !important;
  color: var(--card-text-color) !important;
  position: absolute !important;
  right: 12px !important;
  top: 16px !important;
  bottom: 13px !important;
  cursor: pointer !important;
  z-index: 4 !important;
  text-decoration: underline !important;
}

input::-ms-reveal,
input::-ms-clear {
  display: none;
}

.form-control:disabled {
  background-color: var(--disabled-bg);
  border-color: var(--disabled-bg);
  opacity: 1;
}

.k-disabled,
.k-widget[disabled] {
  background-color: var(--disabled-bg) !important;
  border-color: var(--disabled-bg) !important;
  opacity: 1 !important;
}

.app-sidebar-toggle {
  background-color: transparent !important;
  border: 0px !important;
}

.theme_switch.active {
  color: var(--btn-dark-color) !important;
  border: 1px solid var(--btn-dark-bg) !important;
  background-color: var(--btn-dark-bg) !important;
}

.theme_switch {
  border-radius: 12px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  padding: 7px 15px !important;
  color: var(--card-label-color) !important;
}

.form-control-login {
  height: 44px !important;
  color: var(--Rx-title) !important;
  background-color: var(--Rx-15-F6-color) !important;
  padding: 10px 14px !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  border: 0.5px solid var(--Rx-FFwhite-8Cgray) !important;
}

/* .form-label-login {
  color: var(--Rx-title) !important;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: 500 !important;
  line-height: 20px !important;
} */

/* .form-control-login::placeholder {
  color: #8c8c8c !important;
  font-size: 16px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 24px !important;
} */

.dashboard_icon {
  fill: var(--card-text-color) !important;
  width: 65px;
  height: 65px;
  font-weight: 500;
}

.dashboard-text-main {
  color: var(--card-text-color);
  font-size: 22px;
  margin-bottom: 0px;
  font-weight: 500;
}

.dashboard-text-main-user {
  color: var(--card-text-color);
  font-size: 16px;
  margin-bottom: 0px;
  font-weight: 500;
}

.dashboard-text-sub {
  color: var(--card-text-color);
  font-size: 45px;
  margin-bottom: 0px;
  line-height: 60px;
}

.dashboard-text-sub-user {
  color: var(--card-text-color);
  font-size: 40px;
  margin-bottom: 0px;
  line-height: 60px;
}

.dashboard-card {
  box-shadow: rgba(0, 0, 0, 0.25) 0px 3px 8px !important;
}

.info-div-icon {
  font-size: 25px;
  position: absolute;
  top: 6px;
  right: 10px;
  color: var(--table-body) !important;
}

/* .card{
          background-color: var(--card-bg-body) !important;
      } */

.sidebar-tooltip {
  position: fixed !important;
  left: auto !important;
  z-index: 99999 !important;
  border-color: var(--card-text-color) !important;
  color: var(--btn-dark-color) !important;
  background-color: var(--card-text-color) !important;
}

.modal-sub-text {
  font-size: 13px;
  margin-left: 14px;
  font-weight: 500;
  color: var(--card-label-color);
}

.qr-description {
  height: 133px !important;
}

.simple-table>div>table {
  background-color: var(--card-bg-body) !important;
  border-color: var(--card-text-color);
}

.simple-table>div>table>thead>tr>th {
  color: var(--table-header) !important;
  padding: 8px 12px !important;
  font-size: 13px !important;
  font-weight: inherit !important;
}

.simple-table .step tr th {
  font-weight: bold !important;
}

.simple-table>div>table>tbody>tr>td {
  color: var(--table-body) !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
  font-weight: inherit !important;
  vertical-align: middle;
}

.status-text {
  color: white !important;
}

.view-form-lable {
  font-size: 17px !important;
}

.view-form-data {
  color: var(--card-label-color) !important;
  font-weight: 300 !important;
}

.form-control-color {
  width: 60px !important;
  height: 29px !important;
}

.nav-link {
  padding: 10px 60px !important;
  margin: 0 !important;
}

.nav-link.active {
  background-color: var(--sidebar-menu-bg-dark) !important;
  color: var(--sidebar-menu-bg-light) !important;
}

.nav-link.active:hover {
  color: var(--sidebar-menu-bg-light) !important;
}

.nav-link:hover {
  border: none !important;
  color: var(--sidebar-menu-bg-dark) !important;
}

.image-action-btn {
  padding: 6px 8px !important;
  font-size: 0px !important;
  border-radius: 4px !important;
}

.img-action-icon {
  font-size: 16px;
  margin-right: 0px !important;
}

.preview-image {
  /* background-size: cover; */
  height: 100%;
  width: 100%;
}

.image_video_thumb {
  /* max-width: 160px; */
  width: 50px !important;
  height: 50px !important;
  border-radius: 12px;
}

.user-img-thumb {
  max-width: 50px;
  width: 50px;
  height: 50px;
  border-radius: 50px;
}

.img-video-div {
  border-bottom: 1px solid var(--card-text-color);
}

.title-div {
  font-size: 17px;
  font-weight: 500;
  color: var(--card-text-color);
}

.des-div {
  font-size: 14px;
  font-weight: 500;
  color: var(--card-text-color);
  text-align: justify;
}

.date-time-div {
  font-size: 14px;
  font-weight: 500;
  color: var(--card-text-color);
}

.no-div-found {
  font-size: 15px;
  font-weight: 500 !important;
  color: var(--card-text-color);
}

.badge {
  display: inline-flex;
  align-items: center;
  font-weight: 500;
}

[data-kt-app-sidebar-fixed="true"] .app-sidebar .react-tooltip {
  visibility: hidden;
}

[data-kt-app-sidebar-minimize="on"] .app-sidebar .react-tooltip {
  visibility: visible;
}

.custom-tag {
  color: var(--btn-dark-color) !important;
  border: 1px solid var(--btn-dark-bg) !important;
  background-color: var(--btn-dark-bg) !important;
  padding: 2px 8px;
  border-radius: 4px;

  margin-right: 5px;
  margin-left: 5px;
}

.close_icon {
  color: var(--btn-dark-color) !important;
}

.timediv {
  margin-left: 32px;
  font-size: 12px;
}

/* start tab css */
.bank-content span {
  color: #8c8c8c;
}

.Fry-edit {
  height: 42px;
  width: 42px;
  background-color: #f6f6f6;
  border: 1px solid #e1e1e1;
  border-radius: 50%;
  margin-left: 10px;
  cursor: pointer;
}

.Fry-edit-icon {
  position: relative;
  /* left: 8px; */
  top: 9px;
  right: 8px;
  font-size: 22px;
  text-align: center;
  color: #000;
}

.nav-line-tabs {
  /* border-bottom-color: #999999; */
  border: none !important;
  display: inline-flex;
  flex-wrap: nowrap;
  white-space: nowrap;
  /* overflow-y: auto; */
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 10px;
}

.nav-line-tabs .nav-item .nav-link {
  border-radius: 100px !important;
  padding: 9px 14px 9px 14px !important;
  border: 1px solid gray !important;
  margin-right: 15px !important;
  font-size: 14px;
  /* background: #F5F5F5 !important; */
  color: var(--card-label-color);
}

.attachments .nav-item .nav-link {
  border-radius: 100px !important;
  /* padding: 9px 50px 9px 50px !important; */
  border: 1px solid gray !important;
  /* margin-right: 15px !important; */
  font-size: 14px;
  text-align: center !important;
  /* background: #F5F5F5 !important; */
  color: var(--card-label-color);
}

.nav-line-tabs .nav-item .nav-link.active,
.attachments .nav-item .nav-link.active {
  color: #ffffff !important;
  /* border: 1px solid var(--btn-dark-bg) !important; */
  background-color: var(--Rx-35blue-15black) !important;
}

.nav-line-tabs .nav-item .nav-link.active {
  color: #ffffff !important;
  /* border: 1px solid var(--btn-dark-bg) !important; */
  background-color: var(--Rx-35blue-15black) !important;
}

.ticket-filter svg {
  position: relative;
  left: -4px !important;
}

/* end tab css */

/* Start Action Css */
.k-table-row {
  position: relative;
}

.menu-sub {
  display: none;
  position: absolute;
  right: 35px;
  top: 22px;
}

.menu-hover a {
  cursor: pointer;
}

.menu-hover:hover .menu-sub,
.menu-hover:hover .energy-menu-sub,
.menu-hover:hover .company-menu-sub {
  display: block;
}

.bottom-10px {
  bottom: 70px !important;
}

.document-table .table-responsive {
  overflow-x: visible !important;
}

.company-menu-sub {
  display: none;
  position: absolute;
  right: 5%;
  bottom: 30px;
}

.energy-menu-sub {
  display: none;
  position: absolute;
  right: 40px;
  bottom: 22px;
}

/* End Action Css */

.lat_long_map {
  height: calc(100vh - 102px) !important;
  width: 100% !important;
  border: none !important;
}

.issue_location_map {
  height: calc(100vh - 188px) !important;
  width: 100% !important;
  border: none !important;
}

.spinner-page {
  height: 100%;
  width: 100%;
  background-color: var(--Rx-bg);
  opacity: 0.5 !important;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
}

.spinner-page .spinner {
  /* border-width: 5px !important; */
  border-top-color: var(--Rx-35blue-white) !important;
  border-right-color: var(--Rx-35blue-white) !important;
  border-left-color: var(--Rx-35blue-white) !important;
  /* border-top-color: var(--Rx-35blue-white) !important; */
}

.pac-container {
  z-index: 9999;
}

.lat_long_map {
  height: calc(100vh - 111px) !important;
  width: 100% !important;
  border: none !important;
}

.file-input-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 0px !important;
  margin-top: 0px !important;
}

.file-upload-btn {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  line-height: 25px !important;
}

.csv-remove {
  position: absolute;
  top: 10px;
  right: 124px;
  color: red;
  cursor: pointer;
}

.singledropdown {
  position: relative;
}

.singledropdownicon {
  position: absolute;
  top: 16px;
  right: 5px;
  cursor: text !important;
  /* font-size: 15px; */
}

.singledropdown-closeicon {
  position: absolute;
  top: 4px;
  right: 5px;
}

.k-svg-i-x {
  display: none !important;
}

.h-w-80px {
  height: 80px !important;
  width: 99px !important;
}

.top-20px {
  top: 20px !important;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield;
}

.gm-style-iw-d>div {
  color: #000000 !important;
  font-weight: 500 !important;
  padding: 10px !important;
}

/* Hide the date icon on specific class */
.no-date-icon::-webkit-calendar-picker-indicator {
  display: none;
  -webkit-appearance: none;
  appearance: none;
}

.no-date-icon {
  position: relative;
  z-index: 1;
}

.h-w-40px {
  height: 40px !important;
  width: 40px !important;
}

.w-130px {
  width: 130px !important;
}

.gm-style-iw-chr {
  display: none !important;
}

.Question-answer-modal ol {
  padding-left: 0;
  /* Removes default padding */
  list-style: none;
  /* Removes default list styling */
}

.Question-answer-modal ol li {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  /* Aligns items to the top */
  margin-bottom: 5px;
  /* Space between list items */
}

.Question-answer-modal ol {
  counter-reset: item;
  /* Resets the counter for the list */
}

.Question-answer-modal ol li::before {
  content: counter(item) ". ";
  counter-increment: item;
  margin-right: 10px;
  /* Adjust this value to match the "a" tag's left margin */
  font-weight: bold;
}

.Question-answer-modal ol li a {
  text-decoration: none;
  word-break: break-all;
  margin-left: 10px;
  /* Match the ::before pseudo-element's margin-right */
  margin-top: -5px;
}

main,
span,
ol,
ul,
pre,
div {
  scrollbar-width: thin;
  scrollbar-color: var(--Rx-35blue-white) transparent !important;
}

.k-column-title {
  font-weight: 500 !important;
}

.hwp-instruction-section {
  height: calc(100vh - 260px) !important;
  overflow-y: scroll !important;
}

.hwp-instruction-section {
  height: calc(100vh - 260px) !important;
  overflow-y: scroll !important;
}

[data-kt-app-layout=dark-sidebar] .app-sidebar .menu .menu-item .menu-link.active .menu-icon,
[data-kt-app-layout=dark-sidebar] .app-sidebar .menu .menu-item .menu-link.active .menu-icon .svg-icon,
[data-kt-app-layout=dark-sidebar] .app-sidebar .menu .menu-item .menu-link.active .menu-icon i {
  color: #F5F5F5 !important;
}

.app-content {
  padding-top: 24px !important;
}

.pac-container:after {
  /* Disclaimer: not needed to show 'powered by Google' if also a Google Map is shown */

  background-image: none !important;
  height: 0px;
}

.border-white-black {
  border-color: var(--border-companylight) !important;
}

/* switch css */
.switches-container {
  width: 16rem;
  position: relative;
  display: flex;
  padding: 0;
  position: relative;
  background: var(--Rx-35blue-15black);
  line-height: 3rem;
  border-radius: 1rem;
  border: 0.5px solid #8c8c8c !important;
  /* margin-left: auto;
  margin-right: auto; */
}

/* input (radio) for toggling. hidden - use labels for clicking on */
.switches-container input {
  visibility: hidden;
  position: absolute;
  top: 0;
}

/* labels for the input (radio) boxes - something to click on */
.switches-container label {
  width: 50%;
  padding: 0;
  margin: 0;
  text-align: center;
  cursor: pointer;
  color: white;
}

/* switch highlighters wrapper (sliding left / right) 
  - need wrapper to enable the even margins around the highlight box
*/
.switch-wrapper {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 50%;
  padding: 0.15rem;
  z-index: 0;
  transition: transform .5s cubic-bezier(.77, 0, .175, 1);
  /* transition: transform 1s; */
}

/* switch box highlighter */
.switch_toggle {
  border-radius: 1rem;
  background: white;
  height: 100%;
}

/* switch box labels
  - default setup
  - toggle afterwards based on radio:checked status 
*/
.switch_toggle div {
  width: 100%;
  text-align: center;
  opacity: 0;
  display: block;
  color: var(--Rx-35blue-15black);
  transition: opacity .2s cubic-bezier(.77, 0, .175, 1) .125s;
  will-change: opacity;
  position: absolute;
  top: 0;
  left: 0;
}

.switches-container input[type="radio"] {
  width: 0 !important;
}

/* slide the switch box from right to left */
.switches-container input:nth-of-type(1):checked~.switch-wrapper {
  transform: translateX(0%);
}

/* slide the switch box from left to right */
.switches-container input:nth-of-type(2):checked~.switch-wrapper {
  transform: translateX(100%);
}

/* toggle the switch box labels - first checkbox:checked - show first switch div */
.switches-container input:nth-of-type(1):checked~.switch-wrapper .switch_toggle div:nth-of-type(1) {
  opacity: 1;
}

/* toggle the switch box labels - second checkbox:checked - show second switch div */
.switches-container input:nth-of-type(2):checked~.switch-wrapper .switch_toggle div:nth-of-type(2) {
  opacity: 1;
}

.view-vendor-log {
  border: 1px solid var(--border-companylight);
  font-size: 12px;
  border-radius: 4px;
}

.module-card {
  height: calc(100vh - 286px) !important;
  overflow-y: auto !important;
}

.list-group-item {
  background-color: #15171c !important;
  color: var(--Rx-title) !important;
}

.member-list-escalation {
  max-height: calc(100vh - 604px);
  overflow: auto;
}

.escalation-card {
  border-radius: 15px !important;
  background: var(--Black-3) !important;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25) !important;
  /* width: 440px !important; */
  position: relative;
  /* top: -97px; */
  padding: 42px 0px 32px 0px !important;
  justify-content: center !important;
  text-align: center !important
}

.day-round {
  /* position: relative; */
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.day-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.day-round label {
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 50%;
  cursor: pointer;
  height: 28px;
  width: 28px;
  position: relative;
  display: inline-block;
}

.day-round label:after {
  content: "";
  position: absolute;
  top: 8px;
  left: 7px;
  height: 6px;
  width: 12px;
  border: 2px solid #fff;
  border-top: none;
  border-right: none;
  opacity: 0;
  transform: rotate(-45deg);
}

.day-round input[type="checkbox"] {
  visibility: hidden;
  position: absolute;
}

.day-round input[type="checkbox"]:checked+label {
  background-color: #66bb6a;
  border-color: #66bb6a;
}

.day-round input[type="checkbox"]:checked+label:after {
  opacity: 1;
}

.day-label {
  margin-top: 6px;
  font-size: 12px;
  text-align: center;
}

.react-time-picker__wrapper {
  border: none !important;
}

.react-time-picker__inputGroup__amPm {
  background-color: var(--Rx-15-F6-color) !important;
}

/* Quil Ck-Editor CSS Start */
.goose-ck .ql-snow.ql-toolbar button:hover .ql-stroke,
.goose-ck .ql-snow .ql-toolbar button:hover .ql-stroke,
.goose-ck .ql-snow.ql-toolbar button:focus .ql-stroke,
.goose-ck .ql-snow .ql-toolbar button:focus .ql-stroke,
.goose-ck .ql-snow.ql-toolbar button.ql-active .ql-stroke,
.goose-ck .ql-snow .ql-toolbar button.ql-active .ql-stroke,
.goose-ck .ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,
.goose-ck .ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,
.goose-ck .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.goose-ck .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.goose-ck .ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,
.goose-ck .ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,
.goose-ck .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.goose-ck .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.goose-ck .ql-snow.ql-toolbar button:hover .ql-stroke-miter,
.goose-ck .ql-snow .ql-toolbar button:hover .ql-stroke-miter,
.goose-ck .ql-snow.ql-toolbar button:focus .ql-stroke-miter,
.goose-ck .ql-snow .ql-toolbar button:focus .ql-stroke-miter,
.goose-ck .ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,
.goose-ck .ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,
.goose-ck .ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.goose-ck .ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.goose-ck .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.goose-ck .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.goose-ck .ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.goose-ck .ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.goose-ck .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,
.goose-ck .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter {
  stroke: var(--color-text);
}

.goose-ck .ql-snow.ql-toolbar button:hover,
.goose-ck .ql-snow .ql-toolbar button:hover,
.goose-ck .ql-snow.ql-toolbar button:focus,
.goose-ck .ql-snow .ql-toolbar button:focus,
.goose-ck .ql-snow.ql-toolbar button.ql-active,
.goose-ck .ql-snow .ql-toolbar button.ql-active,
.goose-ck .ql-snow.ql-toolbar .ql-picker-label:hover,
.goose-ck .ql-snow .ql-toolbar .ql-picker-label:hover,
.goose-ck .ql-snow.ql-toolbar .ql-picker-label.ql-active,
.goose-ck .ql-snow .ql-toolbar .ql-picker-label.ql-active,
.goose-ck .ql-snow.ql-toolbar .ql-picker-item:hover,
.goose-ck .ql-snow .ql-toolbar .ql-picker-item:hover,
.goose-ck .ql-snow.ql-toolbar .ql-picker-item.ql-selected,
.goose-ck .ql-snow .ql-toolbar .ql-picker-item.ql-selected {
  color: var(--color-text);
}

.goose-ck .ql-snow.ql-toolbar button:hover .ql-fill,
.goose-ck .ql-snow .ql-toolbar button:hover .ql-fill,
.goose-ck .ql-snow.ql-toolbar button:focus .ql-fill,
.goose-ck .ql-snow .ql-toolbar button:focus .ql-fill,
.goose-ck .ql-snow.ql-toolbar button.ql-active .ql-fill,
.goose-ck .ql-snow .ql-toolbar button.ql-active .ql-fill,
.goose-ck .ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,
.goose-ck .ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,
.goose-ck .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,
.goose-ck .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,
.goose-ck .ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,
.goose-ck .ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,
.goose-ck .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.goose-ck .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.goose-ck .ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,
.goose-ck .ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill,
.goose-ck .ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,
.goose-ck .ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,
.goose-ck .ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,
.goose-ck .ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,
.goose-ck .ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.goose-ck .ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.goose-ck .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.goose-ck .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.goose-ck .ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.goose-ck .ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.goose-ck .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,
.goose-ck .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {
  fill: var(--color-text);
}

.goose-ck .ql-snow .ql-picker.ql-expanded .ql-picker-options {
  display: block;
  margin-top: 0px;
  top: 100%;
  left: 0px;
  z-index: 1;
}

.goose-ck .ql-snow .ql-picker.ql-header {
  border: 1px solid var(--color-text);
  border-radius: 5px;
}

.goose-ck .ql-snow .ql-fill,
.goose-ck .ql-snow .ql-stroke.ql-fill {
  fill: var(--color-text);
}

.goose-ck .ql-snow .ql-stroke {
  fill: transparent;
  stroke: var(--color-text);
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-width: 2;
}

.goose-ck .ql-snow .ql-picker-options {
  background-color: var(--ck-editor-header-collapse);
  border-radius: 5px;

}

.goose-ck .ql-snow .ql-picker.ql-header .ql-picker-label::before,
.goose-ck .ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: 'Normal';
  color: var(--color-text);
}

.goose-ck .ql-toolbar {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  background-color: var(--ql-header-bg);
}

.goose-ck .ql-container {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  background-color: var(--ql-body-bg);
}

.goose-ck .ql-editor {
  height: calc(100vh - 400px) !important;
  overflow-y: auto !important;
}

.goose-ck .ql-snow .ql-tooltip {
  position: absolute;
  top: -10px !important;
  transform: translateY(10px);
  left: 230px !important;
  background-color: var(--ck-editor-body);
  border-radius: 5px !important;
  border: 1px solid var(--active_menu-bg);
  box-shadow: none !important;
  color: var(--active_menu-color);
  font-weight: 600;
}

.goose-ck .ql-action {
  color: var(--color-text) !important;
  font-weight: 600;
}

.goose-ck .ql-snow .ql-picker-options .ql-picker-item {
  padding-bottom: 3px !important;
  padding-top: 3px !important;
}

.goose-ck .ql-toolbar.ql-snow .ql-formats:last-child {
  margin-right: 0 !important;
}

.goose-ck .ql-toolbar.ql-snow {
  border: 1px solid #ccc !important;
}

/* Quil Ck-Editor CSS End */

/* end other css */

/* start tab css */
/* .nav-line-tabs {
  border-bottom-color: #999999;
}

.nav-line-tabs .nav-item .nav-link {
  border-radius: 8px 8px 0px 0px !important;
  padding: 1rem 4rem 0.7rem 4rem !important;
  border: 1px solid #999999 !important;
  margin-right: 15px !important;
  font-size: 14px;
  background: #F5F5F5 !important;
  color: var(--card-label-color);
}

.nav-line-tabs .nav-item .nav-link.active {
  color: var(--btn-dark-color) !important;
  border: 1px solid var(--btn-dark-bg) !important;
  background-color: var(--btn-dark-bg) !important;
} */

/* end tab css */

/* Start timepicker css */

.custoom-timepicker .k-animation-container {
  width: -webkit-fill-available !important;
  border-radius: 10px !important;
  color: var(--Rx-title) !important;
}

.custoom-timepicker .k-animation-container .k-time-list::before,
.k-time-list::after {
  box-shadow: none !important;
}

.custoom-timepicker .k-animation-container .k-title {
  color: var(--Rx-title) !important;
}

.custoom-timepicker .k-animation-container .k-time-list-container .k-time-list-wrapper .k-title {
  margin-bottom: 5px !important;
}

.custoom-timepicker .k-animation-container .k-time-list-container .k-time-list-wrapper .k-time-list .k-time-container {
  background-color: var(--Rx-bg) !important;
}

.custoom-timepicker .k-animation-container .k-popup {
  background-color: var(--Rx-15-F6-color) !important;
  border-radius: 10px !important;
}

.custoom-timepicker .k-animation-container .k-button-flat-base .k-button-text {
  background: var(--Rx-btn-bg) !important;
  color: var(--Rx-bg) !important;
  font-size: 13px !important;
  padding: 3px 10px !important;
  border-radius: 5px !important;
}

.custoom-timepicker .k-animation-container .k-timeselector {
  background-color: var(--Rx-15-F6-color) !important;
  border-radius: 10px !important;
}

.custoom-timepicker .k-animation-container .k-actions-stretched .k-time-cancel {
  background-color: var(--Rx-25black-72ligtblue) !important;
  color: white !important;
  padding: 9px 20px;
  font-size: 16px;
  font-style: normal;
  line-height: 24px;
  border-radius: 8px;
  border: 1px solid var(--Rx-white-72ligtblue) !important;

}

.custoom-timepicker .k-animation-container .k-actions-stretched .k-time-cancel:hover {
  color: var(--Rx-white-72ligtblue) !important;
  padding: 9px 20px;
  font-size: 16px;
  font-style: normal;
  line-height: 24px;
  border-radius: 8px;
  border: 1px solid var(--Rx-25black-72ligtblue) !important;
}

.custoom-timepicker .k-animation-container .k-actions-stretched .k-time-accept {
  background: var(--Rx-btn-bg) !important;
  color: var(--Rx-bg) !important;
  padding: 9px 20px !important;
  font-size: 16px !important;
  font-style: normal !important;
  line-height: 24px !important;
  border-radius: 8px !important;
  border: 1px solid transparent !important;
}

.custoom-timepicker .k-animation-container .k-actions-stretched .k-time-accept:hover {
  background-color: var(--Rx-15-F6-color) !important;
  border: 1px solid var(--Rx-btn-bg) !important;
  color: var(--Rx-btn-bg) !important;
  padding: 9px 20px;
  font-size: 16px;
  font-style: normal;
  line-height: 24px;
  border-radius: 8px;
}

/* start swalfire css */
.swal2-popup {
  background-color: var(--card-bg-body);
  padding: 2rem;
  border-radius: 0.475rem;
}

.swal2-popup .swal2-title {
  font-weight: 500;
  font-size: 1.3rem;
  color: var(--card-text-color);
}

.swal2-popup .swal2-html-container,
.swal2-popup .swal2-content {
  color: var(--table-body) !important;
}

/* end swalfire css */

/* start breadcrumb-item css */
.breadcrumb .breadcrumb-item {
  display: flex !important;
  align-items: center !important;
  justify-content: start !important;
}

.breadcrumb-item span {
  color: #8c8c8c;
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  padding: 0.75rem 0.5rem;
  margin-right: 0.5rem;
  text-decoration: none;
  cursor: pointer;
  user-select: none;
}

.breadcrumb-item.activetab span,
.breadcrumb-item:hover span {
  color: var(--Rx-title) !important;
}

/* end breadcrumb-item css */

/* start scrollbar css */
main,
span,
ol,
ul,
pre,
div {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-color) transparent;
}

main:hover,
span:hover,
ol:hover,
ul:hover,
pre:hover,
div:hover {
  scrollbar-color: var(--scrollbar-color) transparent;
}

/* end scrollbar css */

/* start set new password css. */
.set-new-password-title-section {
  text-align: center !important;
}

.set-new-password-title {
  color: var(--Rx-bg);
  text-align: center !important;
  font-family: Inter !important;
  font-size: 40px !important;
  font-style: normal !important;
  font-weight: 500 !important;
  line-height: 56px !important;
}

.set-new-password-content {
  color: var(--Gray-1) !important;
  text-align: center !important;
  font-size: 16px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 24px !important;
}

/* end set new password css */

/* start get-started css. */

.get-started-content {
  color: var(--Gray-1);
  text-align: center;
  font-size: 16px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 24px !important;
}

.get-started-footer-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px !important;
}

.get-started-footer-text {
  color: var(--Gray-1);
  text-align: center;
  font-size: 18px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 28px !important;
}

/* .get-started-footer-contact-text {
  color: var(--Rx-bg) !important;
  text-align: center;
  font-size: 16px !important;
  font-style: normal !important;
  font-weight: 500 !important;
  line-height: 24px !important;
} */
/* .get-started-btn-back-login {
  color: var(--Rx-title) !important;
  text-align: center;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: 500 !important;
  line-height: 20px !important;
} */

/* .get-started-btn-continue-text {
  color: var(--Rx-btn-bg) !important;
  font-size: 16px !important;
  font-style: normal !important;
  font-weight: 500 !important;
  line-height: 24px !important;
} */
/* end get-started css. */

/* start terms and conditions css */
/* .term-condition-width {
  scrollbar-width: thin !important;
  scrollbar-color: white !important;
} */
.terms-and-conditions {
  width: 100% !important;
  margin: 0 !important;
  padding: 0px 16px;
}

.terms-and-conditions .col-12 {
  padding: 0 !important;
  margin: 0 !important;
}

.terms-and-conditions-card {
  width: 100% !important;
  border-radius: 24px;
  background: var(--Black-2);
  padding: 40px !important;
  border: none !important;
  height: calc(100vh - 130px) !important;
}

.terms-and-conditions-card .card-title {
  color: var(--Rx-title);
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 36px;
}

.terms-and-conditions-card .card-text {
  color: var(--Rx-title);
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 28px;
}

.terms-and-conditions-card-body {
  width: 100%;
  height: calc(100vh - 360px) !important;
  overflow-y: scroll !important;
  overflow-x: hidden !important;
  padding: 0px 6px 0 0 !important;
  scrollbar-width: thin !important;
  scrollbar-color: #888 transparent !important;
}

.terms-and-conditions-card-body p,
.terms-and-conditions-card-body span {
  color: var(--Gray-1) !important;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  padding: 0 !important;
  margin: 0 !important;
  text-align: justify;
}

.terms-and-conditions-card-body h4 {
  margin-bottom: 15px;
}

.terms-and-conditions-card-body h3 {
  margin-bottom: 15px;
}

.terms-and-conditions-card-body h5 {
  font-size: 17px !important;
  margin-bottom: 7px !important;
}

.terms-and-conditions-card .row {
  align-items: center !important;
}

.terms-and-conditions-card .check-term-condition-text {
  color: var(--Rx-title);
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  cursor: pointer;
  /* margin-bottom: 10px !important; */
}

/* start card css */
.custom-card {
  background-color: var(--Rx-15-F6-color) !important;
  border-radius: 10px;
  color: var(--Rx-title);
}

.custom-card .text_white_email {
  color: var(--Rx-title);
}

.fs-12px {
  font-size: 12px !important;
}

.fs-14px {
  font-size: 14px !important;
}

.fs-10px {
  font-size: 10px !important;
}

.fp-search-wrapper {
  margin-top: 5px;
}

.rx-search {
  padding-left: 35px !important;
}

.form-control.rx-search {
  margin-top: 0 !important;
}

/* end card css */
/* change */
input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 20px;
  height: 20px;
  background-color: var(--Rx-15-F6-color);
  border: 1px solid var(--Rx-btn-bg) !important;
  border-radius: 5px;
  cursor: pointer;
  position: relative;
}

/* Style the checkbox when checked */
input[type="checkbox"]:checked {
  background-color: white;
  /* border: 1px solid var(--Rx-btn-bg); */
}

/* Custom checkmark */
input[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  top: 3px;
  left: 6px;
  width: 5px;
  height: 10px;
  border: solid black;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Hide the default radio button */
input[type="radio"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 20px;
  height: 20px;
  background-color: var(--Rx-bg);
  border: 0.5px solid var(--Rx-35blue-white);
  border-radius: 50%;
  cursor: pointer;
  position: relative;
}

/* start ticket css */
.tag-edit svg path {
  fill: var(--Rx-title);
}

.filter-div {
  position: relative;
}

.filter-div .filter-count {
  position: absolute;
  top: 0px;
  right: -6px;
  background-color: red;
  font-size: 7px;
  font-weight: 900;
  height: 15px;
  width: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  user-select: none;
}

.vector-img {
  border-radius: 20px;
  box-sizing: border-box;
  color: #000;
  padding: 3px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in;
  -webkit-tap-highlight-color: transparent;
  background-color: white !important;
}

.vector-img-icon {
  box-sizing: content-box;
  cursor: pointer;
  font-size: 18px;
  color: #000;
}

.audio-recorder {
  box-shadow: none !important;
  background-color: var(--Rx-15black-ECwhite) !important;
}

.recording.audio-recorder {
  background-color: var(--Rx-FFwhite-8Cgray) !important;
}

.audio-recorder-mic,
.audio-recorder-options {
  padding: 3px !important;
  height: 18px !important;
  background-color: white !important;
}

.recording.audio-recorder .audio-recorder-mic,
.recording.audio-recorder .audio-recorder-options {
  background-color: transparent !important;
}

.custom-datepicker .k-daterangepicker {
  width: 100% !important;
}

.custom-datepicker .k-calendar-container {
  border: none !important;
  border-color: none !important;
}

.custom-datepicker .k-daterangepicker.k-disabled {
  width: 100% !important;
  background-color: var(--Rx-bg) !important;
}

.custom-datepicker .k-daterangepicker .k-floating-label-container {
  gap: 10px !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: start !important;
  padding-top: 0 !important;
}

.custom-datepicker .k-dateinput {
  width: 100% !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  padding: 7px 10px !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  margin-top: 5px !important;
  border: 0.5px solid #8c8c8c !important;
  color: var(--Rx-title) !important;
  background-color: var(--Rx-15-F6-color) !important;
}

.k-calendar .k-calendar-td.k-selected .k-calendar-cell-inner,
.k-calendar .k-calendar-td.k-selected .k-link {
  background-color: var(--Rx-title) !important;
  color: var(--Rx-15-F6-color) !important;
}

.custom-datepicker .k-calendar-header,
.custom-datepicker .k-calendar-header button {
  background-color: var(--Rx-25black-E1gray) !important;
  color: var(--Rx-title) !important;
}

.custom-datepicker .k-calendar-view {
  background-color: var(--Rx-25black-E1gray) !important;
  color: var(--Rx-title) !important;
}

.k-calendar .k-range-mid {
  background-color: var(--light-white) !important;
  color: var(--btn-dark-color) !important;
}

.k-calendar-td {
  border-radius: 0 !important;
}

.k-calendar-td.k-disabled {
  opacity: 0.4 !important;
}

/* end ticket css */

/* start Ticket Detail css */

.detail-toggle-container {
  margin: 20px;
  text-align: center;
}

.details {
  overflow: hidden;
  max-height: 446px;
  transition: max-height 0.5s ease-out;
  position: relative;
}

.details.expanded {
  max-height: 700px;
  /* Adjust as needed */
  transition: max-height 0.5s ease-in;
}

.details>.show-hide {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 10px;
  background-color: var(--Rx-15-F6-color);
}

.details>.show-hide>span {
  cursor: pointer;
  background-color: var(--Rx-15black-ECwhite);
  border-radius: 10px;
  padding: 1px 7px;
  font-size: 12px;
  user-select: none;
  color: var(--Rx-title);
}

.badgediv {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 20px;
  position: relative;
}

.badgediv .badge {
  position: absolute;
  top: 10px;
  right: 6px;
  color: white !important;
}

.badgediv svg g path {
  fill: var(--Rx-title) !important;
}

.ticket-detail-left {
  position: relative;
}

.comment-section {
  height: calc(100vh - 280px);
  overflow-y: auto;
}

.height-comment {
  height: calc(100vh - 150px);
  overflow-y: auto;
}

.comment-section .comment-card {
  height: fit-content !important;
}

.ticket-detail-right {
  max-height: calc(100vh - 148px);
  overflow-y: scroll;
}

.ticket-detail-chat {
  position: sticky;
  bottom: 0;
  right: 0;
  z-index: 3;
  max-height: 160px;
}

.event-member-img {
  padding: 8px;
  background-color: var(--Rx-bg);
  border-radius: 50%;
}

.k-calendar-navigation {
  display: none !important;
}

.k-calendar-infinite,
.k-content {
  background-color: var(--Rx-15-F6-color) !important;
  border-radius: 10px !important;
  color: var(--Rx-title) !important;
}

.k-calendar-infinite .k-calendar-view::after {
  box-shadow: none !important;
}

.post-btn {
  padding: 2px 10px !important;
  font-size: 12px !important;
}

.right-25px {
  right: 25px !important;
}

.custom-dropdown {
  width: 93px !important;
  height: 18px !important;
  color: white !important;
  font-size: 10px !important;
}

.custom-dropdown .k-input-inner {
  padding: 2px 0 2px 6px !important;
}

.custom-dropdown .k-input-button {
  padding: 2px 0px !important;
}

.open-emoji {
  opacity: 1;
  transition: top 1s ease-in-out, opacity 1s ease-in-out;
  top: 30% !important;
  z-index: 1 !important;
  display: block !important;
}

.close-emoji {
  top: 50% !important;
  opacity: 0;
  transition: top 1s ease-in-out, opacity 1s ease-in-out;
  z-index: -1;
  display: block !important;
}

.emoji-picker-container {
  overflow: hidden;
  position: absolute;
  top: 40%;
  right: 0;
  z-index: -1;
}

.emoji-picker-container div {
  height: 40vh;
  width: 100%;
  user-select: none;
}

.emoji-picker-container em-emoji-picker {
  /* --background-rgb: rgba(21,21,21,1); */
  --border-radius: 10px;
  --category-icon-size: 24px;
  --color-border-over: rgba(0, 0, 0, 0.1);
  --color-border: rgba(0, 0, 0, 0.05);
  --font-family: "Inter", sans-serif !important;
  --font-size: 15px;
  --rgb-accent: 255, 255, 255;
  --rgb-background: var(--rxb);
  --rgb-color: 255, 255, 255;
  --rgb-input: 0, 0, 0;
  height: 40vh;
  width: 100%;
}

.comment-files {
  max-height: 50px;
  overflow-y: auto;
}

/* .custom-dropdown .k-list-item {
  font-size: 10px !important;
} */

.k-chat-ticket {
  height: calc(100vh - 150px) !important;
}

.attachment-files-inTicket {
  max-width: 100%;
  height: 190px;
  object-fit: fill;
  margin-bottom: 4px;
}

.location-container {
  max-width: calc(100vw - 65vw);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.react-timekeeper .react-timekeeper__top-bar,
.react-timekeeper .react-timekeeper__clock-wrapper {
  background-color: var(--Rx-35blue-15black) !important;
  color: var(--Rx-title) !important;
}

.css-13arinl-meridiem,
.css-1roj3d7-meridiem {
  background-color: #b1e2fa !important;
}

.react-timekeeper .react-timekeeper__clock-wrapper .react-timekeeper__clock {
  background-color: var(--Rx-bg) !important;
  color: var(--Rx-title) !important;
}

.p-datepicker.p-component {
  background-color: var(--Rx-15black-ECwhite) !important;
  border: none !important;
}

.p-datepicker-header {
  background-color: var(--Rx-15black-ECwhite) !important;
  border: none !important;
}

.p-datepicker-header button,
.p-datepicker-header .p-datepicker-title {
  color: var(--Rx-title) !important;
}

.p-datepicker-header button:hover,
.p-datepicker-header .p-datepicker-title:hover {
  color: var(--Rx-title) !important;
  background-color: var(--Rx-15black-ECwhite) !important;
}

.p-datepicker-calendar tbody tr td {
  overflow: hidden !important;
  padding: 3px !important;
  color: var(--Rx-title) !important;
  border-radius: 5px !important;
}

.p-datepicker-calendar tbody tr .p-datepicker-today span {
  border-radius: 5px !important;
  background-color: var(--Gray-1) !important;
  color: var(--Rx-bg) !important;
}

.p-datepicker-calendar tbody tr .p-highlight {
  border-radius: 5px !important;
  background-color: var(--Rx-title) !important;
  color: var(--Rx-bg) !important;
}

.p-datepicker-calendar tbody tr td span:hover {
  border-radius: 5px !important;
  background-color: var(--Rx-btn-bg) !important;
  color: var(--Rx-bg) !important;
}

.p-datepicker-calendar tbody tr td span {
  border-radius: 5px !important;
}

.p-datepicker-calendar thead tr th {
  padding: 10px !important;
}

.p-inputtext {
  border-radius: 6px !important;
  font-weight: 500 !important;
  color: var(--Rx-title) !important;
  background-color: var(--Rx-15-F6-color) !important;
  padding: 10px 14px !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  border: 0.5px solid #8c8c8c !important;
}

.p-inputtext:focus {
  outline: none !important;
  border: 1px solid var(--Rx-35blue-white) !important;
  outline: 0 !important;
  box-shadow: none !important;
}

.p-inputtext:hover {
  outline: none !important;
  border: 1px solid var(--Rx-35blue-white) !important;
  outline: 0 !important;
  box-shadow: none !important;
}

.audio-react-recorder__canvas {
  border-radius: 10px;
  margin-top: 6px;
  margin-right: 6px;
}

.audio-save-btn-time {
  position: absolute;
  top: 0px;
  left: 0px;
  background-color: white;
  padding: 3px 4px;
  border-bottom-left-radius: 10px;
  border-top-left-radius: 10px;
  margin-top: 6px;
}

.pause-audio-btn {
  position: absolute;
  top: 0px;
  right: 30px;
  background-color: white;
  padding: 3px 4px;
  border-bottom-right-radius: 10px;
  border-top-right-radius: 10px;
  margin-top: 6px;
  margin-right: 6px;
}

.k-panelbar {
  border: none !important;
  background-color: transparent !important;
}

.k-panelbar .k-panelbar-item {
  background-color: var(--rx-sidenav-header-bg) !important;
  margin-top: 9px !important;
  border-radius: 5px !important;
  /* max-height: calc(100vh - 70vh) !important;
  overflow-y: auto !important; */
}

.loop_vendor_detail .k-panelbar-item .k-animation-container {
  height: calc(100vh - 320px) !important;
  overflow-y: auto !important;
  /* height: 47vh; */
}

.k-panelbar .k-link {
  cursor: pointer !important;
  background-color: transparent !important;
  color: var(--Gray-1) !important;
}

.k-panelbar .k-link.k-selected {
  /* background-color: var(--sidebar-menu-bg-dark) !important; */
  color: var(--Rx-title) !important;
  border: none !important;
}

.text-orange {
  color: #fc9403;
}

.custom-Tag {
  background: linear-gradient(var(--btn-color), var(--btn-color)) padding-box,
    linear-gradient(to right, darkblue, darkorchid) border-box;
  border-radius: 50em;
  border: 2px solid transparent;
  font-size: 13px;
  padding: 5px 10px;
  border-style: dashed !important;
}

.goosechat-tag-container {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  border-radius: 10px;
  padding: 11px 20px;
  border: 1px solid var(--rx-sidenav-help);
}

.ticket-detail-right-description {
  width: 160px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
}

/* end Ticket Detail css */

/* Start Department css */
.k-grid-aria-root {
  border: none !important;
  border-top-left-radius: 6px !important;
  border-top-right-radius: 6px !important;
}

.k-grid {
  background-color: transparent !important;
}

.table_div>div>div {
  border: none !important;
}

.k-grid-container .k-grid-content {
  border: none !important;
  overflow-y: scroll !important;
}

.k-column-title {
  white-space: nowrap;
  color: var(--table-header) !important;
}

.k-grid .k-grid-header {
  background-color: var(--card-bg-body) !important;
  border-bottom: 1px solid #d6d6d83d !important;
  border: none !important;
}

.tab-view-table .k-grid-container .k-grid-content {
  height: calc(100vh - 500px);
}

.saftyform-table .k-grid-container .k-grid-content {
  height: calc(100vh - 393px);
}

.saftyform-table1 .k-grid-container .k-grid-content {
  height: calc(100vh - 390px);
}

.k-table-th {
  border: 0px !important;
  border-bottom: 1px solid #d6d6d83d !important;
  background-color: var(--card-bg-body) !important;
  white-space: nowrap !important;
}

.k-grid .k-grid-header-wrap {
  width: 100% !important;
  border: none;
}

.k-table-td {
  color: var(--Rx-title) !important;
  font-size: 14px;
}

.k-grid td,
.k-grid th {
  border: 0px !important;
  border-bottom: 1px solid #d6d6d83d !important;
  background-color: var(--card-bg-body) !important;
  white-space: nowrap !important;
  padding: 7px 15px !important;
}

.k-grid tr:nth-child(odd) td {
  background-color: var(--Rx-03black-white) !important;
  /* Light background color for odd rows */
}

.k-grid tr:nth-child(even) td {
  background-color: var(--Rx-15black-ECwhite) !important;
  /* Light background color for even rows */
}

.k-grid-content {
  overflow-y: auto;
  height: calc(100vh - 305px);
  background-color: var(--Rx-bg) !important;
}

/* avoid scrolling in repoeting question table */
#pdf-content .k-grid-content{
  height: calc(100vh - 455px);
}

.k-table-td>a>.k-svg-icon>svg {
  fill: var(--table-body) !important;
}

.k-sort-icon>.k-icon>svg {
  fill: var(--table-body) !important;
}

/* start add_department css */
.add-department-card {
  padding: 20px !important;
}

.add-department-card .add-department-card-header {
  border: none !important;
  width: fit-content !important;
  /* padding: 20px !important; */
  border-radius: 5px;
  position: relative;
  background-color: var(--disabled-bg);
}

.add-department-card-header .add-department-card-plus {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 15px;
  height: 15px;
  text-align: center;
  background-color: white;
  color: black;
  border-radius: 50%;
  font-size: 10px;
}

.add-department-card-header .add-department-card-img {
  font-size: 50px;
}

.add-assign-member .assign-member-btn {
  background-color: var(--disabled-bg) !important;
  padding: 5px 10px !important;
  border-radius: 5px;
}

.close-btn {
  color: var(--Rx-title) !important;
}

.striped-list li:nth-child(odd) {
  background-color: var(--Rx-03black-white);
  /* light gray color */
  border: 1px solid var(--Rx-25black-E1gray);
}

.striped-list li:nth-child(even) {
  background-color: var(--Rx-15black-ECwhite);
  /* white color */
  border: 1px solid var(--Rx-25black-E1gray);
}

/* end add_department css */

/* Start User Management css */
.user-image {
  display: inline-block;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-size: 32px 35px;
  background-position: center center;
  object-fit: cover;
  vertical-align: middle;
  line-height: 36px;
  box-shadow: inset 0 0 1px #999, inset 0 0 10px rgba(0, 0, 0, 0.2);
  /* margin-left: 5px; */
}

.user-status {
  width: 200px;
  height: 35.5px !important;
  margin-left: 10px;
  border-color: rgba(0, 0, 0, 0.08) !important;
}

.noimage-upload-wrapper {
  background-color: #8c8c8c;
  width: 100px;
  height: 100px;
  border-radius: 50%;
}

.noimage-upload-icon {
  position: relative;
  height: 40px;
  width: 40px;
  top: 30px;
  left: 30px;
  color: #f6f6f6;
}

.add-icon {
  height: 16px;
  width: 16px;
  background-color: #fff;
  color: #030303;
  border-radius: 50%;
  border: 1px solid #030303;
}

.add-icon-user {
  height: 20px;
  width: 20px;
  background-color: #fff;
  color: #030303;
  border-radius: 50%;
  border: 1px solid #030303;
}

.upload-btn {
  cursor: pointer;
  position: absolute;
  top: 28px;
  left: 100px;
}

.upload-user-img {
  cursor: pointer;
  position: absolute;
  top: 28px;
  right: 0px;
  left: 146px;
}

.image-input-wrapper {
  width: 100px;
  height: 100px;
  border-radius: 50%;
}

.upload-text {
  font-size: 16px;
  margin-left: 16px;
}

.permission-title {
  font-size: 18px;
}

.permission-list>li {
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0px;
  border-bottom: 1px solid #f6f6f6;
}

.ticket-sub {
  display: flex;
  flex-direction: column;
}

.ticket-sub li {
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0px;
  padding-left: 20px;
  border-top: 1px solid #f6f6f6 !important;
}

.standard-switch.k-switch-on .k-switch-track {
  border-color: #49c481 !important;
  background-color: #49c481 !important;
}

.standard-switch.k-switch-off .k-switch-track {
  border-color: #525151 !important;
  background-color: #525151 !important;
}

.k-switch-md {
  width: 42px !important;
}

.k-switch-md .k-switch-track {
  width: 40px !important;
  height: 20px !important;
  outline: none !important;
}

.k-switch-on .k-switch-track {
  border-color: #355f9b !important;
  color: white !important;
  background-color: #355f9b !important;
}

.k-switch-off .k-switch-track {
  border-color: var(--Rx-15-F6-color) !important;
  color: white !important;
  background-color: var(--Rx-15-F6-color) !important;
}

.k-switch-md .k-switch-thumb {
  height: 17px !important;
  width: 17px !important;
}

.k-switch-md.k-switch-off .k-switch-thumb-wrap {
  left: 11px !important;
}

.k-switch-md.k-switch-on .k-switch-thumb-wrap {
  left: calc(100% - 11px) !important;
}

.notification-switch .k-switch-off .k-switch-track {
  background-color: var(--rx-sidenav-link-bg) !important;
}

.new-user-footer {
  box-shadow: rgba(0, 0, 0, 0.15) 0px 5px 15px 0px;
  background-color: #fff;
  padding: 15px 10px;
  align-items: center;
  position: sticky;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 1;
}

.user-profile-name {
  font-size: 24px;
  font-weight: 600;
}

.user-edit {
  height: 42px;
  width: 42px;
  background-color: var(--Black-3);
  border: 1px solid var(--Black-3);
  border-radius: 50%;
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.user-edit svg path {
  fill: var(--Rx-title);
}

.user-edit-icon {
  position: relative;
  left: 8px;
  top: 8px;
  font-size: 22px;
  text-align: center;
}

.user-profile-details li {
  display: flex;
  justify-content: space-between;
  padding: 6px 0px;
}

.user-profile-details li span {
  font-size: 14px;
  font-weight: 400;
}

.user-profile-details li p {
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 500;
}

.k-tabstrip-items-wrapper {
  border: none !important;
}

.k-tabstrip-items-wrapper.k-hstack {
  margin-bottom: 20px;
}

.k-tabstrip-items .k-item {
  border: none;
}

.k-tabstrip-items-wrapper .k-item.k-active {
  background-color: #355f9b !important;
  color: #fff !important;
  border-radius: 100px;
  border-color: #355f9b !important;
}

.k-tabstrip-items-wrapper .k-item:focus {
  box-shadow: none !important;
}

.k-tabstrip-items-wrapper .k-item:hover,
.k-tabstrip-items-wrapper .k-item {
  color: #030303;
}

.k-tabstrip-content {
  border-color: transparent;
  color: transparent;
  background-color: transparent;
}

.assignlogo {
  /* padding: 5px; */
  /* background-color: rgb(44, 43, 43); */
  border-radius: 5px;
  height: 32px;
  width: 32px;
}

.permission-list p,
.permissions-list label {
  padding: 20px 15px;
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* End User Management css */

/* start add_assign_modal css */
.memberlogo {
  padding: 10px;
  background-color: rgb(44, 43, 43);
  border-radius: 5px;
}

/* end add_assign_modal css */

/* End Department css */

/* Start Incedent css */
.incident-report-textarea {
  min-height: 200px !important;
  width: 100%;
  border-radius: 6px !important;
  font-weight: 500 !important;
  color: var(--Rx-title) !important;
  background-color: var(--Rx-15-F6-color) !important;
  padding: 10px 14px !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  margin-top: 5px !important;
  border: 0.5px solid #8c8c8c !important;
}

.incident-report-textarea:focus {
  outline: none !important;
  border: 1px solid var(--Rx-35blue-white) !important;
  outline: 0 !important;
  box-shadow: none !important;
}

.incident-report-textarea:hover {
  outline: none !important;
  border: 1px solid var(--Rx-35blue-white) !important;
  outline: 0 !important;
  box-shadow: none !important;
}

.incident-report-textarea::placeholder {
  color: #8c8c8c !important;
  font-size: 16px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 24px !important;
}

/* End Incedent css */

/* Style the radio button when checked */
input[type="radio"]:checked {
  background-color: var(--Rx-bg);
  border: 0.5px solid var(--Rx-35blue-white);
}

/* Custom dot */
input[type="radio"]:checked::after {
  content: "";
  position: absolute;
  top: 4px;
  left: 4px;
  width: 10px;
  height: 10px;
  background-color: var(--Rx-35blue-white);
  border-radius: 50%;
}

.Department-edit-icons {
  position: relative;
  right: 9px;
  top: 9px;
  font-size: 22px;
  text-align: center;
  color: #000;
}

/* change till here*/

/* end terms and conditions css */

/* start Footer css */
.app-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--Rx-bg);
  border-top: 1px solid var(--Rx-15-F6-color);
  height: 40px !important;
  z-index: 999;
}

/* end Footer css */

.custom-card-container {
  position: relative;
  /* Ensure the overlay is positioned relative to this container */
}

.loader-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  /* Semi-transparent background */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  /* Ensure it sits above the content */
}

.custom-card-container .custom-card {
  position: relative;
  /* Ensure the content is stacked below the overlay */
  z-index: 1;
}

/* Stpper */
/* DONE STEP */
.rx-stepper.k-stepper .k-step-done .k-step-indicator {
  background-color: var(--stepper-active-bg);
  /* Background for done steps */
  color: var(--stepper-active-color);
  /* Text/icon color for done steps */
  border-color: var(--stepper-border-color);
  /* Border color for done steps */
}

/* in active step */
.rx-stepper.k-stepper .k-step-indicator {
  background-color: var(--stepper-bg);
  color: var(--message-text);
  border-color: var(--stepper-border-color);
}

/* Current step state */
.rx-stepper.k-stepper .k-step-current .k-step-indicator {
  background-color: var(--stepper-current-bg);
  /* Background for the current step */
  color: var(--stepper-bg);
  /* Text/icon color for the current step */
  border-color: var(--stepper-current-bg);
  /* Border color for the current step */
}

.rx-stepper.k-stepper .k-step-indicator:hover {
  background-color: var(--stepper-bg);
  /* Background for the current step */
  color: var(--stepper-color);
  /* Text/icon color for the current step */
  border-color: var(--stepper-border-color);
  /* Border color for the current step */
}

.rx-stepper.k-stepper .k-step-indicator {
  width: 25px;
  height: 25px;
}

.rx-stepper.k-stepper .k-step-indicator-text {
  font-size: 10px;
}

/* Line progress bar */
.rx-stepper .k-progressbar .k-selected {
  background-color: var(--stepper-active-bg);
  /* Color for the selected part of the progress bar */
}

.rx-stepper .k-step-list-horizontal~.k-progressbar {
  top: 15px;
}

/* text */
.rx-stepper .k-step-text {
  font-size: 12px;
}

.vendor_companyname {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 100%;
  /* Use full available width */
  display: block;
  font-weight: 500;
}

/* Add this to ensure proper flex layout */
.company-container {
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 0;
  /* Important for text truncation */
}

.company-info {
  flex: 1;
  min-width: 0;
  /* Important for text truncation */
}

.company-state {
  font-size: 12px;
  color: #666;
  display: block;
  margin-top: 2px;
}

/* Ensure user image doesn't shrink */
.user-image {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
}

/* start mediaquery */
/* @media only screen and (min-width:1440){
          
      } */

@media only screen and (min-width: 1024px) and (max-width: 1439px) {
  .product-card {
    width: 360px !important;
  }

  .issue_location_map {
    height: calc(100vh - 188px) !important;
  }
}

@media screen and (max-width: 991px) {
  .inner-content-wrapper {
    padding-bottom: 30px;
  }

  .save-continue-footer {
    box-shadow: rgba(0, 0, 0, 0.15) 0px 5px 15px 0px;
    position: fixed;
    bottom: 40px;
    width: 100%;
    z-index: 1;
    margin: 0px 0px 0px -20px;
  }

  .issue_location_map {
    height: calc(100vh - 198px) !important;
  }

}

@media only screen and (min-width: 768px) and (max-width: 1023px) {
  .login-left {
    /* border-radius: 0px !important;
              background-color: #eeeeee !important;
              padding: 67px 68px 0px 68px !important;
              box-shadow: 0px 2px 2px 0px #00000040 !important;
              width: 100% !important;
              justify-content: center !important;
              height: 496px; */
    display: none !important;
  }

  .inlogin-footre-version {
    display: flex !important;
  }

  .left-footer-text {
    font-size: 16px;
  }

  .right-main-header {
    /* font-family: 'Roboto Slab' !important; */
    font-size: 24px !important;
    font-weight: 500 !important;
    line-height: 52.75px !important;
    color: var(--Rx-title) !important;
    margin-bottom: 20px !important;
  }

  .welcome-text {
    font-size: 33px !important;
    font-weight: 500 !important;
    line-height: 52.75px !important;
    color: var(--Rx-title) !important;
  }

  .product-card {
    border-radius: 15px;
    background: rgba(255, 255, 255, 1) !important;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25) !important;
    width: 340px !important;
    height: 239px !important;
    position: relative !important;
    top: -97px !important;
    padding: 33px 0px 33px 0px !important;
    display: flex !important;
    align-items: end !important;
    justify-content: center !important;
    text-align: center !important;
  }

  .product-dsc p {
    font-size: 13px !important;
    font-weight: 500 !important;
    line-height: 20px !important;
    color: var(--Rx-title) !important;
  }

  .login-left-footer {
    display: none !important;
  }

  .login-right {
    height: 100% !important;
    padding: 0px 0px !important;
    width: 100% !important;
    justify-content: center !important;
  }

  .form-width {
    margin-top: 0px;
    width: 355px !important;
  }

  .login-right-footer {
    display: none !important;
  }

  .logo-image-mobile {
    height: 180px !important;
    width: 180px !important;
    padding-top: 20px !important;
  }

  .mobile-logo-div {
    display: block !important;
  }

  .terms-and-conditions-card .check-term-condition-text {
    font-size: 10px;
    margin-bottom: 10px !important;
  }

  .mobile-padding {
    padding: 0px !important;
  }

  .mobile-margin-bottom-0 {
    margin-bottom: 0px !important;
  }

  .otp-box-margin {
    margin-right: 5px;
  }

  .mobile-text-center {
    text-align: center !important;
  }

  .get-started-content {
    font-size: 14px !important;
  }

  .terms-and-conditions-card {
    height: calc(100vh - 220px) !important;
  }

  .k-grid .k-grid-md .k-table-th,
  .k-grid-md .k-table-th {
    width: 130px !important;
  }

  .k-grid .k-grid-md td,
  .k-grid .k-grid-md .k-table-td,
  .k-grid-md td,
  .k-grid-md .k-table-td {
    width: 130px !important;
  }

  .rx-btn,
  .btn-success {
    padding: 4px 10px !important;
    font-size: 11px !important;
  }

  .form-control {
    height: 36px !important;
  }

  .k-multiselect {
    padding: 6px 15px !important;
  }

  .issue_location_map {
    height: calc(100vh - 174px) !important;
  }
}

@media only screen and (max-width: 768px) {
  .version-popup {
    left: 0px;
  }


}

@media only screen and (max-width: 767px) {
  .login-left {
    /* border-radius: 0px !important;
              background-color: #eeeeee !important;
              padding: 67px 68px 0px 68px !important;
              box-shadow: 0px 2px 2px 0px #00000040 !important;
              width: 100% !important;
              justify-content: center !important;
              height: 496px; */
    display: none !important;
  }

  .inlogin-footre-version {
    display: flex !important;
  }

  .left-footer-text {
    font-size: 14px;
  }

  .right-main-header {
    /* font-family: 'Roboto Slab' !important; */
    font-size: 22px !important;
    font-weight: 500 !important;
    line-height: 52.75px !important;
    color: var(--Rx-title) !important;
    margin-bottom: 20px !important;
  }

  .add-department-footer {
    position: sticky !important;
  }

  .welcome-text {
    font-size: 33px !important;
    font-weight: 500 !important;
    line-height: 52.75px !important;
    color: #0d0e12 !important;
  }

  .product-card {
    border-radius: 15px;
    background: rgba(255, 255, 255, 1) !important;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25) !important;
    width: 340px !important;
    height: 239px !important;
    position: relative !important;
    top: -97px !important;
    padding: 33px 0px 33px 0px !important;
    display: flex !important;
    align-items: end !important;
    justify-content: center !important;
    text-align: center !important;
  }

  .product-dsc p {
    font-size: 13px !important;
    font-weight: 500 !important;
    line-height: 20px !important;
    color: #0d0e12 !important;
  }

  .login-left-footer {
    display: none !important;
  }

  .login-right {
    height: 100% !important;
    padding: 0px 0px !important;
    width: 100% !important;
    justify-content: center !important;
  }

  .form-width {
    margin-top: 0px;
    width: 355px !important;
  }

  .login-right-footer {
    display: none !important;
  }

  .logo-image-mobile {
    height: 180px !important;
    width: 180px !important;
    padding-top: 20px !important;
  }

  .mobile-logo-div {
    display: block !important;
  }

  .mobile-padding {
    padding: 0px !important;
  }

  .mobile-margin-bottom-0 {
    margin-bottom: 0px !important;
  }

  .otp-box-margin {
    margin-right: 5px;
  }

  .mobile-text-center {
    text-align: center !important;
  }

  .search-box {
    width: 100% !important;
    height: 28.5px !important;
  }

  .btn-dark {
    border-radius: 12px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    padding: 4px 11px !important;
    color: var(--btn-dark-color) !important;
    border: 1px solid var(--btn-dark-bg) !important;
    background-color: var(--btn-dark-bg) !important;
  }

  .btn-icon-custom {
    font-size: 15px !important;
    margin-right: 5px !important;
  }

  .page-title {
    font-size: 21px;
    font-weight: 500;
  }

  h2 {
    font-size: 14px !important;
  }

  .mobile-margin {
    margin-bottom: 10px !important;
  }

  .otp-heade-one {
    font-size: 18px !important;
  }

  .otp-heade-two {
    font-size: 12px !important;
  }

  .otp-heade-two span {
    font-size: 12px !important;
  }

  .otpdiv>div {
    color: var(--Rx-bg) !important;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 28px;
  }

  .otpdiv>div>span>span {
    color: var(--Gray-1);
  }

  .otpdiv>div>button>span,
  .otpdiv>.re-send-button>span {
    color: var(--Gray-1);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 28px;
  }

  .terms-and-conditions-card {
    padding: 20px !important;
    height: calc(100vh - 230px) !important;
  }

  .terms-and-conditions-card .card-title {
    line-height: 30px;
    font-size: 20px !important;
  }

  .terms-and-conditions-card .card-text {
    line-height: 18px;
    font-size: 15px !important;
  }

  .terms-and-conditions-card .check-term-condition-text {
    font-size: 10px;
    margin-bottom: 10px !important;
  }

  .terms-and-conditions-card-body p,
  .terms-and-conditions-card-body h6 {
    font-size: 10px;
  }

  .k-grid .k-grid-md .k-table-th,
  .k-grid-md .k-table-th {
    width: 130px !important;
  }

  .k-grid .k-grid-md td,
  .k-grid .k-grid-md .k-table-td,
  .k-grid-md td,
  .k-grid-md .k-table-td {
    width: 130px !important;
  }

  .width-50 {
    width: 50%;
  }

  .width-30 {
    width: 30%;
  }

  .width-70 {
    width: 70%;
  }

  .dashboard_icon {
    fill: var(--card-text-color) !important;
    width: 45px;
    height: 45px;
    font-weight: 500;
  }

  .get-started-content {
    font-size: 14px !important;
  }

  .continue-btn .btn-login-page-term {
    height: 36px !important;
    padding: 0px 0px !important;
    font-size: 10px !important;
  }

  .location-container {
    max-width: calc(100vw - 35vw) !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .rx-btn,
  .btn-success {
    padding: 4px 10px !important;
    font-size: 11px !important;
  }

  .form-control {
    height: 36px !important;
  }

  .k-multiselect {
    padding: 6px 15px !important;
  }

  /* .mobile-text-start{
              text-align: left !important;
          } */
}

@media only screen and (min-width: 320px) and (max-width: 480px) {
  .form-width {
    margin-top: 0px;
    width: 296px !important;
  }

  .inlogin-footre-version {
    display: flex !important;
  }

  .left-footer-text {
    font-size: 13px;
  }

  .right-footer span {
    font-size: 14px;
    color: var(--Gray-1);
  }

  .right-footer-text {
    font-size: 14px !important;
    color: var(--Rx-btn-bg) !important;
  }

  .get-started-content {
    font-size: 12px !important;
  }

  .otp-heade-one {
    font-size: 18px !important;
  }

  .terms-and-conditions-card {
    padding: 20px !important;
  }

  .terms-and-conditions-card .card-title {
    line-height: 30px;
    font-size: 20px !important;
  }

  .btn-success {
    background-color: green;
    border-color: green;
    color: white;
  }

  .terms-and-conditions-card .card-text {
    line-height: 18px;
    font-size: 15px !important;
  }

  .terms-and-conditions-card .check-term-condition-text {
    font-size: 10px;
  }

  .terms-and-conditions-card-body {
    height: calc(100vh - 410px) !important;
  }

  .terms-and-conditions-card-body p,
  .terms-and-conditions-card-body h6 {
    font-size: 10px;
  }

  .continue-btn .btn-login-page-term {
    height: 36px !important;
    padding: 0px 0px !important;
    font-size: 10px !important;
  }

  .get-started-content {
    padding: 0px !important;
    margin: 0px !important;
  }

  .need-help-text {
    top: 10px !important;
    right: 10px !important;
  }

  .get-started-footer-text {
    margin: 0px !important;
  }

  .k-calendar-infinite .k-calendar-view {
    padding-inline: 10px !important;
  }

  .input-space {
    margin-top: 0px;
  }

  .input-space-checkbox {
    margin-bottom: 8px;
  }

  /* .form-control {
    margin-bottom: 8px;
  } */
  .file-input-container .file-name span {
    width: 100px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
  }

  .issue_location_map {
    height: calc(100vh - 240px) !important;
  }

  /* .vendor_companyname {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    width: 220px;
    display: block;
  } */
}

/* end mediaquery */

/* end mediaquery */
/* Map Css */
.gm-style-iw-d {
  overflow: hidden !important;
}

/* .gm-style-iw-d div{
  padding: 0px !important;
  width: 100px;
  height: 100px;
} */
.gm-style-iw {
  padding-left: 0 !important;
}

.gm-style-iw-d>div>div>.img {
  height: 100%;
  width: 100%;
  border-radius: 50%;
  border: 2px solid #0066cd;
}

/* Map Css */