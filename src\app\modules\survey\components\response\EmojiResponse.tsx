import React, { useState, useEffect } from 'react';

interface EmojiOption {
  value: string;
  icon: string;
  label: string;
}

interface EmojiResponseProps {
  options: EmojiOption[];
  value: string;
  onChange: (value: string) => void;
  readOnly?: boolean;
}

const EmojiResponse: React.FC<EmojiResponseProps> = ({ options, value, onChange, readOnly }) => {
  const [selected, setSelected] = useState(value);

  useEffect(() => {
    setSelected(value);
  }, [value]);

  const handleSelect = (val: string) => {
    setSelected(val);
    onChange(val);
  };

  return (
    <div style={{ display: 'flex', gap: 24 }}>
      {options.map((opt) => (
        <div
          key={opt.value}
          onClick={() => readOnly ? null : handleSelect(opt.value)}
          style={{
            border: selected === opt.value ? '2px solid #007bff' : '1px solid #ccc',
            borderRadius: 8,
            padding: 16,
            cursor: readOnly ? 'not-allowed' : 'pointer',
            background: selected === opt.value ? '#e6f0ff' : '#fff',
            fontSize: 32,
            minWidth: 56,
            textAlign: 'center',
            boxShadow: selected === opt.value ? '0 0 8px #007bff33' : 'none',
          }}
        >
          {opt.icon}
        </div>
      ))}
    </div>
  );
};

export default EmojiResponse; 