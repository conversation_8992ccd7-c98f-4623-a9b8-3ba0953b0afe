import { FilterState } from "../types/chartTypes";
import { SurveyResponseTableRow } from "../types/chartTypes";
import { TransformedRespondentResponse } from "../utils/responseDataTransformer";

// Filter types for different components
export type FilterType = 'charts' | 'responses' | 'comments' | 'global';

// Filter configuration interface
export interface FilterConfig {
  id: string;
  name: string;
  type: FilterType;
  field: string;
  operator: 'equals' | 'contains' | 'in' | 'between' | 'greaterThan' | 'lessThan' | 'dateRange' | 'scoreRange';
  value: unknown;
  isActive: boolean;
}

// Filter result interface
export interface FilterResult<T> {
  data: T[];
  totalCount: number;
  filteredCount: number;
  appliedFilters: FilterConfig[];
}

// Base filter interface
export interface BaseFilter {
  id: string;
  name: string;
  type: FilterType;
  isActive: boolean;
}

// Specific filter interfaces
export interface PropertyFilter extends BaseFilter {
  properties: string[];
}

export interface DepartmentFilter extends BaseFilter {
  departments: string[];
}

export interface StatusFilter extends BaseFilter {
  statuses: string[];
}

export interface DateRangeFilter extends BaseFilter {
  startDate: Date | null;
  endDate: Date | null;
}

export interface ScoreRangeFilter extends BaseFilter {
  minScore: number | null;
  maxScore: number | null;
}

export interface ResponseTypeFilter extends BaseFilter {
  responseTypes: string[];
}

// Combined filter state
export interface CombinedFilterState {
  property?: PropertyFilter;
  department?: DepartmentFilter;
  status?: StatusFilter;
  dateRange?: DateRangeFilter;
  score?: ScoreRangeFilter;
  responseType?: ResponseTypeFilter;
  [key: string]: BaseFilter | undefined;
}

class FilterService {
  private static instance: FilterService;
  private filters: Map<string, CombinedFilterState> = new Map();

  private constructor() {}

  public static getInstance(): FilterService {
    if (!FilterService.instance) {
      FilterService.instance = new FilterService();
    }
    return FilterService.instance;
  }

  // Apply filters to chart data
  public filterChartData<T extends { questionId: string; responseType: string }>(
    data: T[],
    filters: CombinedFilterState
  ): FilterResult<T> {
    let filteredData = [...data];
    const appliedFilters: FilterConfig[] = [];

    // Apply response type filter
    if (filters.responseType?.isActive && filters.responseType.responseTypes.length > 0) {
      filteredData = filteredData.filter(item => 
        filters.responseType!.responseTypes.includes(item.responseType)
      );
      appliedFilters.push({
        id: 'responseType',
        name: 'Response Type',
        type: 'charts',
        field: 'responseType',
        operator: 'in',
        value: filters.responseType.responseTypes,
        isActive: true
      });
    }

    return {
      data: filteredData,
      totalCount: data.length,
      filteredCount: filteredData.length,
      appliedFilters
    };
  }

  // Apply filters to response list data
  public filterResponseListData(
    data: TransformedRespondentResponse[],
    filters: CombinedFilterState
  ): FilterResult<TransformedRespondentResponse> {
    let filteredData = [...data];
    const appliedFilters: FilterConfig[] = [];

    // Apply property filter
    if (filters.property?.isActive && filters.property.properties.length > 0) {
      filteredData = filteredData.filter(item => 
        item.property && filters.property!.properties.includes(item.property)
      );
      appliedFilters.push({
        id: 'property',
        name: 'Property',
        type: 'responses',
        field: 'property',
        operator: 'in',
        value: filters.property.properties,
        isActive: true
      });
    }

    // Apply department filter
    if (filters.department?.isActive && filters.department.departments.length > 0) {
      filteredData = filteredData.filter(item => 
        item.department && filters.department!.departments.includes(item.department)
      );
      appliedFilters.push({
        id: 'department',
        name: 'Department',
        type: 'responses',
        field: 'department',
        operator: 'in',
        value: filters.department.departments,
        isActive: true
      });
    }

    // Apply status filter
    if (filters.status?.isActive && filters.status.statuses.length > 0) {
      filteredData = filteredData.filter(item => 
        item.status && filters.status!.statuses.includes(item.status)
      );
      appliedFilters.push({
        id: 'status',
        name: 'Status',
        type: 'responses',
        field: 'status',
        operator: 'in',
        value: filters.status.statuses,
        isActive: true
      });
    }

    // Apply date range filter
    if (filters.dateRange?.isActive && (filters.dateRange.startDate || filters.dateRange.endDate)) {
      filteredData = filteredData.filter(item => {
        const itemDate = new Date(item.submittedAt);
        const startDate = filters.dateRange!.startDate;
        const endDate = filters.dateRange!.endDate;

        if (startDate && endDate) {
          return itemDate >= startDate && itemDate <= endDate;
        } else if (startDate) {
          return itemDate >= startDate;
        } else if (endDate) {
          return itemDate <= endDate;
        }
        return true;
      });
      appliedFilters.push({
        id: 'dateRange',
        name: 'Date Range',
        type: 'responses',
        field: 'submittedAt',
        operator: 'dateRange',
        value: { start: filters.dateRange.startDate, end: filters.dateRange.endDate },
        isActive: true
      });
    }

    // Apply score range filter
    if (filters.score?.isActive && (filters.score.minScore !== null || filters.score.maxScore !== null)) {
      filteredData = filteredData.filter(item => {
        if (!item.score) return false;
        
        const score = item.score;
        const minScore = filters.score!.minScore;
        const maxScore = filters.score!.maxScore;

        if (minScore !== null && maxScore !== null) {
          return score >= minScore && score <= maxScore;
        } else if (minScore !== null) {
          return score >= minScore;
        } else if (maxScore !== null) {
          return score <= maxScore;
        }
        return true;
      });
      appliedFilters.push({
        id: 'score',
        name: 'Score Range',
        type: 'responses',
        field: 'score',
        operator: 'scoreRange',
        value: { min: filters.score.minScore, max: filters.score.maxScore },
        isActive: true
      });
    }

    return {
      data: filteredData,
      totalCount: data.length,
      filteredCount: filteredData.length,
      appliedFilters
    };
  }

  // Apply filters to comments data
  public filterCommentsData(
    data: SurveyResponseTableRow[],
    filters: CombinedFilterState
  ): FilterResult<SurveyResponseTableRow> {
    let filteredData = [...data];
    const appliedFilters: FilterConfig[] = [];

    // Filter only COMMENT type questions
    filteredData = filteredData.filter(item => item.responseType === 'COMMENT');

    // Apply date range filter to comments (if applicable)
    if (filters.dateRange?.isActive && (filters.dateRange.startDate || filters.dateRange.endDate)) {
      // Note: This would need to be implemented based on actual comment data structure
      // For now, we'll just mark it as applied
      appliedFilters.push({
        id: 'dateRange',
        name: 'Date Range',
        type: 'comments',
        field: 'submittedAt',
        operator: 'dateRange',
        value: { start: filters.dateRange.startDate, end: filters.dateRange.endDate },
        isActive: true
      });
    }

    return {
      data: filteredData,
      totalCount: data.length,
      filteredCount: filteredData.length,
      appliedFilters
    };
  }

  // Convert FilterState to CombinedFilterState
  public convertFilterState(filterState: FilterState): CombinedFilterState {
    const combinedFilters: CombinedFilterState = {};

    if (filterState.property) {
      combinedFilters.property = {
        id: 'property',
        name: 'Property',
        type: 'responses',
        isActive: true,
        properties: Array.isArray(filterState.property) ? filterState.property : [filterState.property]
      };
    }

    if (filterState.department) {
      combinedFilters.department = {
        id: 'department',
        name: 'Department',
        type: 'responses',
        isActive: true,
        departments: Array.isArray(filterState.department) ? filterState.department : [filterState.department]
      };
    }

    if (filterState.status) {
      combinedFilters.status = {
        id: 'status',
        name: 'Status',
        type: 'responses',
        isActive: true,
        statuses: Array.isArray(filterState.status) ? filterState.status : [filterState.status]
      };
    }

    if (filterState.dateRange) {
      combinedFilters.dateRange = {
        id: 'dateRange',
        name: 'Date Range',
        type: 'responses',
        isActive: true,
        startDate: filterState.dateRange.start,
        endDate: filterState.dateRange.end
      };
    }

    if (filterState.score) {
      combinedFilters.score = {
        id: 'score',
        name: 'Score Range',
        type: 'responses',
        isActive: true,
        minScore: filterState.score.min,
        maxScore: filterState.score.max
      };
    }

    return combinedFilters;
  }

  // Store filters for a specific component
  public setFilters(componentId: string, filters: CombinedFilterState): void {
    this.filters.set(componentId, filters);
  }

  // Get filters for a specific component
  public getFilters(componentId: string): CombinedFilterState | undefined {
    return this.filters.get(componentId);
  }

  // Clear filters for a specific component
  public clearFilters(componentId: string): void {
    this.filters.delete(componentId);
  }

  // Get active filter count
  public getActiveFilterCount(filters: CombinedFilterState): number {
    return Object.values(filters).filter(filter => filter?.isActive).length;
  }

  // Validate filter configuration
  public validateFilter(filter: FilterConfig): boolean {
    if (!filter.id || !filter.name || !filter.field) {
      return false;
    }

    switch (filter.operator) {
      case 'in':
        return Array.isArray(filter.value) && filter.value.length > 0;
      case 'between':
      case 'dateRange':
      case 'scoreRange':
        return Boolean(filter.value && typeof filter.value === 'object' && filter.value !== null && 
               ('start' in filter.value || 'end' in filter.value));
      default:
        return filter.value !== undefined && filter.value !== null;
    }
  }

  // Create a new filter configuration
  public createFilter(
    id: string,
    name: string,
    type: FilterType,
    field: string,
    operator: FilterConfig['operator'],
    value: unknown
  ): FilterConfig {
    return {
      id,
      name,
      type,
      field,
      operator,
      value,
      isActive: true
    };
  }
}

export default FilterService; 