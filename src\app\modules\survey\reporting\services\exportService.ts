// Enhanced export service for survey reporting
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { ExportOptions, ChartData, SurveyResponse } from "../types/chartTypes";

// Type definition for XLSX
interface XLSXModule {
  utils: {
    book_new: () => any;
    json_to_sheet: (data: any[]) => any;
    book_append_sheet: (workbook: any, sheet: any, name: string) => void;
  };
  writeFile: (workbook: any, filename: string) => void;
}

// Dynamic import for XLSX to ensure Vite compatibility
const loadXLSX = async (): Promise<XLSXModule> => {
  try {
    const XLSX = await import("xlsx");
    return XLSX as XLSXModule;
  } catch (error) {
    console.error("Failed to load XLSX library:", error);
    throw new Error(
      "Excel export functionality is not available. Please ensure the xlsx library is properly installed."
    );
  }
};

// PDF Export Configuration
interface PDFExportConfig {
  scale: number;
  backgroundColor: string;
  width: number;
  pageWidth: number;
  pageHeight: number;
  margin: number;
}

const DEFAULT_PDF_CONFIG: PDFExportConfig = {
  scale: 2,
  backgroundColor: '#ffffff',
  width: 1200,
  pageWidth: 210, // A4 width in mm
  pageHeight: 297, // A4 height in mm
  margin: 20,
};

export class ExportService {
  /**
   * Export table as PDF with exact UI preservation
   */
  static async exportTableToPDF(
    tableSelector: string,
    options: ExportOptions & { config?: Partial<PDFExportConfig> }
  ): Promise<void> {
    const config = { ...DEFAULT_PDF_CONFIG, ...options.config };
    const { fileName = "table-export.pdf" } = options;

    try {
      const tableElement = this.getTableElement(tableSelector);
      const processedTable = await this.prepareTableForPDF(tableElement, config);
      const canvas = await this.captureTableAsCanvas(processedTable, config);
      const pdf = await this.createPDFFromCanvas(canvas, config);
      
      pdf.save(fileName);
      console.log("PDF export completed successfully");
    } catch (error) {
      console.error("Error generating PDF:", error);
      throw new Error(`Failed to generate PDF: ${error}`);
    }
  }

  /**
   * Export simplified Q&A data to Excel
   */
  static async exportQAToExcel(
    responses: SurveyResponse[],
    options: ExportOptions
  ): Promise<void> {
    const { fileName = "survey-qa.xlsx" } = options;

    try {
      const XLSX = await loadXLSX();
      const workbook = XLSX.utils.book_new();
      const qaSheet = this.prepareQASheet(responses, XLSX);
      
      XLSX.utils.book_append_sheet(workbook, qaSheet, "Q&A");
      XLSX.writeFile(workbook, fileName);
      
      console.log("Excel export completed successfully");
    } catch (error) {
      console.error("Error generating Excel file:", error);
      throw new Error(`Failed to generate Excel file: ${error}`);
    }
  }

  // Private helper methods
  private static getTableElement(selector: string): HTMLElement {
    const element = document.querySelector(selector) as HTMLElement;
    if (!element) {
      throw new Error(`Table element not found with selector: ${selector}`);
    }
    return element;
  }

  private static async prepareTableForPDF(
    tableElement: HTMLElement, 
    config: PDFExportConfig
  ): Promise<HTMLElement> {
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.top = '0';
    container.style.backgroundColor = config.backgroundColor;
    container.style.padding = '20px';
    container.style.width = `${config.width}px`;

    const clonedTable = this.cloneAndStyleTable(tableElement, config);
    container.appendChild(clonedTable);
    document.body.appendChild(container);

    return container;
  }

  private static cloneAndStyleTable(
    tableElement: HTMLElement, 
    config: PDFExportConfig
  ): HTMLElement {
    const tableContainer = tableElement.closest('.card-body') || tableElement.parentElement;
    if (!tableContainer) {
      throw new Error("Table container not found");
    }

    const clonedContainer = tableContainer.cloneNode(true) as HTMLElement;
    this.applyTableStyles(clonedContainer, config);
    this.removeUIElements(clonedContainer);
    this.addTableTitle(clonedContainer);
    this.expandTableContent(clonedContainer);

    return clonedContainer;
  }

  private static applyTableStyles(container: HTMLElement, config: PDFExportConfig): void {
    Object.assign(container.style, {
      backgroundColor: '#1e1e1e', // Dark theme background
      padding: '20px',
      margin: '0',
      border: 'none',
      boxShadow: 'none',
      fontFamily: 'Arial, sans-serif',
      fontSize: '12px',
      lineHeight: '1.4',
      color: '#ffffff', // White text for dark theme
    });
  }

  private static removeUIElements(container: HTMLElement): void {
    const selectors = [
      '.k-pager', 
      '.k-pager-wrap', 
      '.k-pager-numbers',
      '.k-grid-toolbar',
      '.k-grid-header-wrap',
      '.k-grid-footer',
      '.k-grid-toolbar',
      '.k-grid-header',
      '.k-grid-header-wrap'
    ];
    
    selectors.forEach(selector => {
      container.querySelectorAll(selector).forEach(el => el.remove());
    });
  }

  private static addTableTitle(container: HTMLElement): void {
    const title = document.createElement('h3');
    title.textContent = 'Survey Response List';
    title.style.marginBottom = '20px';
    title.style.color = '#ffffff'; // White text for dark theme
    title.style.fontSize = '18px';
    title.style.fontWeight = 'bold';
    title.style.borderBottom = '2px solid #007bff';
    title.style.paddingBottom = '10px';
    container.insertBefore(title, container.firstChild);
  }

  private static expandTableContent(container: HTMLElement): void {
    // Remove height restrictions and show all content
    const scrollContainers = container.querySelectorAll('.k-grid-content, .k-table-tbody');
    scrollContainers.forEach((element: Element) => {
      const el = element as HTMLElement;
      el.style.height = 'auto';
      el.style.maxHeight = 'none';
      el.style.overflow = 'visible';
    });

    // Preserve original dark theme styling
    const tables = container.querySelectorAll('table, .k-grid');
    tables.forEach((table: Element) => {
      const tableEl = table as HTMLElement;
      tableEl.style.width = '100%';
      tableEl.style.borderCollapse = 'collapse';
      tableEl.style.backgroundColor = '#1e1e1e'; // Dark background
      tableEl.style.color = '#ffffff'; // White text
    });

    // Style table headers - preserve dark theme
    const headers = container.querySelectorAll('th, .k-table-th');
    headers.forEach((header: Element) => {
      const headerEl = header as HTMLElement;
      headerEl.style.backgroundColor = '#2d2d2d'; // Dark header background
      headerEl.style.color = '#ffffff'; // White text
      headerEl.style.fontWeight = 'bold';
      headerEl.style.padding = '12px 8px';
      headerEl.style.border = '1px solid #404040';
      headerEl.style.textAlign = 'center';
      headerEl.style.fontSize = '13px';
    });

    // Style table cells - preserve dark theme
    const cells = container.querySelectorAll('td, .k-table-td');
    cells.forEach((cell: Element) => {
      const cellEl = cell as HTMLElement;
      cellEl.style.padding = '10px 8px';
      cellEl.style.border = '1px solid #404040';
      cellEl.style.textAlign = 'center';
      cellEl.style.fontSize = '12px';
      cellEl.style.verticalAlign = 'middle';
      cellEl.style.backgroundColor = '#1e1e1e'; // Dark background
      cellEl.style.color = '#ffffff'; // White text
      cellEl.style.fontWeight = 'normal';
      
      // Preserve original text colors for special content
      const content = cellEl.innerHTML;
      if (content.includes('😊') || content.includes('😞') || 
          content.includes('👍') || content.includes('👎') ||
          content.includes('Yes') || content.includes('No') ||
          content.includes('Good') || content.includes('Bad')) {
        cellEl.style.fontSize = '14px';
        cellEl.style.fontWeight = 'bold';
        cellEl.style.color = '#ffffff'; // Keep white text
      }
    });

    // Style alternating rows - dark theme
    const rows = container.querySelectorAll('tr, .k-table-row');
    rows.forEach((row: Element, index: number) => {
      const rowEl = row as HTMLElement;
      if (index % 2 === 1) {
        rowEl.style.backgroundColor = '#2d2d2d'; // Darker alternating rows
      } else {
        rowEl.style.backgroundColor = '#1e1e1e'; // Dark background
      }
    });
  }

  private static async captureTableAsCanvas(
    container: HTMLElement, 
    config: PDFExportConfig
  ): Promise<HTMLCanvasElement> {
    return await html2canvas(container, {
      scale: config.scale,
      useCORS: true,
      logging: false,
      backgroundColor: config.backgroundColor,
      width: config.width,
      height: undefined,
      scrollY: 0,
      scrollX: 0,
      onclone: (document: Document) => this.prepareDocumentForCapture(document),
    });
  }

  private static prepareDocumentForCapture(document: Document): Document {
    const allElements = document.querySelectorAll('*');
    allElements.forEach((el: Element) => {
      const element = el as HTMLElement;
      if (element.style) {
        this.fixElementStyles(element);
      }
    });
    
    // Additional fix for text content visibility - preserve dark theme
    const textElements = document.querySelectorAll('.k-table-td, td, th, span, div');
    textElements.forEach((el: Element) => {
      const element = el as HTMLElement;
      if (element.textContent && element.textContent.trim() !== '') {
        element.style.color = '#ffffff'; // White text for dark theme
        element.style.backgroundColor = element.style.backgroundColor || '#1e1e1e'; // Dark background
        element.style.fontWeight = element.style.fontWeight || 'normal';
        element.style.fontSize = element.style.fontSize || '12px';
      }
    });
    
    return document;
  }

  private static fixElementStyles(element: HTMLElement): void {
    // Preserve dark theme styling while ensuring visibility
    if (element.style) {
      // Keep original colors but ensure visibility
      if (element.style.color === 'var(--chart-text)' || 
          element.style.color === '' || 
          element.style.color === 'inherit' ||
          element.style.color === 'currentColor') {
        element.style.color = '#ffffff'; // White text for dark theme
      }
      
      // Ensure background is visible but preserve dark theme
      if (element.style.backgroundColor === 'transparent' || 
          element.style.backgroundColor === 'rgba(0,0,0,0)' ||
          element.style.backgroundColor === '') {
        element.style.backgroundColor = '#1e1e1e'; // Dark background
      }
      
      // Remove height restrictions
      if (element.style.maxHeight) {
        element.style.maxHeight = 'none';
      }
      if (element.style.overflow === 'hidden') {
        element.style.overflow = 'visible';
      }
      
      // Ensure text content is visible with dark theme
      if (element.textContent && element.textContent.trim() !== '') {
        element.style.color = '#ffffff'; // White text
        element.style.fontWeight = 'normal';
      }
    }
    
    // Special handling for table elements - preserve dark theme
    if (element.classList.contains('k-table-td') || 
        element.tagName === 'TD' || 
        element.tagName === 'TH') {
      element.style.color = '#ffffff'; // White text
      element.style.backgroundColor = element.style.backgroundColor || '#1e1e1e'; // Dark background
    }
  }

  private static async createPDFFromCanvas(
    canvas: HTMLCanvasElement, 
    config: PDFExportConfig
  ): Promise<jsPDF> {
    const pdf = new jsPDF("p", "mm", "a4");
    const imgWidth = config.pageWidth;
    const pageHeight = config.pageHeight;
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    
    let heightLeft = imgHeight;
    let position = 0;

    // Add first page
    pdf.addImage(
      canvas.toDataURL("image/png", 1.0),
      "PNG",
      0,
      position,
      imgWidth,
      imgHeight
    );
    heightLeft -= pageHeight;

    // Add additional pages if needed
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(
        canvas.toDataURL("image/png", 1.0),
        "PNG",
        0,
        position,
        imgWidth,
        imgHeight
      );
      heightLeft -= pageHeight;
    }

    return pdf;
  }

  private static prepareQASheet(
    responses: SurveyResponse[], 
    XLSX: XLSXModule
  ) {
    const flatData = responses.flatMap(response =>
      response.answers.map(answer => ({
        Question: answer.questionText,
        Answer: answer.answerText,
      }))
    );

    const sheet = XLSX.utils.json_to_sheet(flatData);
    sheet["!cols"] = [
      { width: 50 }, // Question
      { width: 30 }, // Answer
    ];

    return sheet;
  }

  // Legacy methods for backward compatibility
  static async exportToPDF(options: ExportOptions): Promise<void> {
    return this.exportTableToPDF(".k-grid", options);
  }

  static async exportToExcel(
    chartData: ChartData[],
    responses: SurveyResponse[] = [],
    options: ExportOptions
  ): Promise<void> {
    return this.exportQAToExcel(responses, options);
  }

  static async exportToExcelSimple(
    responses: SurveyResponse[] = [],
    options: ExportOptions
  ): Promise<void> {
    return this.exportQAToExcel(responses, options);
  }
}
