import React from "react";
import ChartComponent from "./ChartComponent";
import { useChartContext } from "../context/ChartContext";
import { useSurveyChartData } from "../context/SurveyDataContext";
import { useFilterService } from "../hooks/useFilterService";
import { FilterState } from "../types/chartTypes";
import { NoData } from "../../../../component";
import { Card } from "react-bootstrap";

interface ChartContainerProps {
  activeTab: string;
  currentFilters?: FilterState;
}

const ChartContainer: React.FC<ChartContainerProps> = ({
  activeTab,
  currentFilters,
}) => {
  const { selectedChartOption } = useChartContext();
  const { getChartData, hasData, isLoading, error } = useSurveyChartData();

  // Initialize filter service for charts
  const { filterChartData, applyFilterState, hasActiveFilters } =
    useFilterService({
      componentId: `charts-${activeTab}`,
      filterType: "charts",
    });

  // Apply filters when they change
  React.useEffect(() => {
    if (currentFilters) {
      applyFilterState(currentFilters);
    }
  }, [currentFilters, applyFilterState]);

  // Get chart data for the selected chart type
  const rawChartData = getChartData(
    selectedChartOption.type as "pie" | "stackedBar"
  );

  // Apply filters to chart data
  const filteredChartData = React.useMemo(() => {
    if (!rawChartData || rawChartData.length === 0) {
      return [];
    }

    if (hasActiveFilters) {
      // Type assertion to handle both pie and stacked bar chart data
      const result = filterChartData(
        rawChartData as Array<{ questionId: string; responseType: string }>
      );
      return result.data;
    }

    return rawChartData;
  }, [rawChartData, hasActiveFilters, filterChartData]);

  if (isLoading) {
    return (
      <div
        className="d-flex justify-content-center align-items-center"
        style={{ minHeight: "200px" }}
      >
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading charts...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-danger" role="alert">
        <h6 className="alert-heading">Chart Error</h6>
        <p className="mb-0">{error}</p>
      </div>
    );
  }

  if (!hasData || filteredChartData.length === 0) {
    return (
      <>
        {/* <div className="alert alert-info" role="alert">
        <h6 className="alert-heading">No Chart Data Available</h6>
        <p className="mb-0">
          {hasActiveFilters
            ? "No chart data matches the current filters. Try adjusting your filter criteria."
            : "No survey response data available for charts. Please ensure the survey has responses."}
        </p>
      </div> */}
      <Card className="p-4 rounded-3 mb-4 custom-card">
        <NoData message="No Data Found" />
      </Card>
      </>
    );
  }

  // Function to render multiple charts based on available questions
  const renderMultipleCharts = () => {
    // Render all charts for all questions in filteredChartData
    return filteredChartData.map((_, index) => (
      <div
        key={`${selectedChartOption.type}-question-${index}`}
        className="col-md-12 mb-4"
      >
        <ChartComponent
          chartType={selectedChartOption.type as "pie" | "stackedBar"}
          questionIndex={index}
        />
      </div>
    ));
  };

  return (
    <>
      <div className="row">
        {/* {activeTab === "goose" && (
          <>
             // Display charts for goose tab
            {renderMultipleCharts()}
          </>
        )} */}
        {activeTab === "summary" && (
          <>
            {/* Display charts for summary tab */}
            {renderMultipleCharts()}
          </>
        )}
        {activeTab === "responses" && (
          <>
            {/* Display charts for responses tab */}
            {renderMultipleCharts()}
          </>
        )}
      </div>
    </>
  );
};

export default ChartContainer;
