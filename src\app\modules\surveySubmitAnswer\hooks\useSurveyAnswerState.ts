import { useState, useCallback } from 'react';
import { useSubmitSurveyResponseMutation } from '../../../apis/survaysAPI';
import { SurveyQuestionResponseDto, ISubmitSurveyResponsePayload, AttachmentDtoList } from '../../../apis/type';

interface SurveyAnswer {
  answers: string[];
  comment: string;
  attachmentDtoList?: AttachmentDtoList[];
  branchingQuestion?: {
    answers: string[];
    comment: string;
    attachmentDtoList?: AttachmentDtoList[];
  };
}

interface SurveyResponseState {
  [questionId: string]: {
    answer: SurveyAnswer;
    attachmentDtoList?: AttachmentDtoList[];
    responseId?: string; // Store the response ID for this question
    branchingResponseId?: string; // Store the response ID for branching question
  };
}

export const useSurveyAnswerState = (surveyId: string) => {
  const [answers, setAnswers] = useState<SurveyResponseState>({});
  const [globalSurveyResponseId, setGlobalSurveyResponseId] = useState<string | undefined>(undefined);
  const [submitSurveyResponse, { isLoading: isSubmitting }] = useSubmitSurveyResponseMutation();

  // Update answer for a specific question
  const updateAnswer = useCallback((questionId: string, answer: SurveyAnswer) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: {
        ...prev[questionId],
        answer,
      }
    }));
  }, []);

  // Submit answer for a specific question
  const handleSubmitAnswer = useCallback(async (
    question: SurveyQuestionResponseDto,
    questionIndex: number
  ) => {
    const currentAnswer = answers[question.id]?.answer;

    if (!currentAnswer) {
      // No answer provided, skip submission
      return;
    }
    console.log('currentAnswer: ', currentAnswer);
    console.log('Current question state:', answers[question.id]);

    try {
      const currentQuestionState = answers[question.id];

      const payload: ISubmitSurveyResponsePayload = {
        surveyId,
        surveyQuestionId: question.id,
        answers: currentAnswer.answers || [],
        comment: currentAnswer.comment || '',
        attachmentDtoList: currentAnswer.attachmentDtoList || [],
        branchingQuestion: currentAnswer.branchingQuestion ? {
          id: currentQuestionState?.branchingResponseId || '', // Use stored branching response ID
          answers: currentAnswer.branchingQuestion.answers || [],
          comment: currentAnswer.branchingQuestion.comment || '',
          surveyQuestionId: question.branchingQuestion?.id || '',
          surveyResponseId: globalSurveyResponseId || '',
          attachmentDtoList: currentAnswer.branchingQuestion.attachmentDtoList || [],
        } : undefined as any,
        // Use global surveyResponseId for all submissions after the first one
        surveyResponseId: globalSurveyResponseId || undefined,
        // Use stored response ID for editing existing responses
        id: currentQuestionState?.responseId || undefined,
      };

      console.log('Submitting payload with IDs:', {
        questionId: question.id,
        responseId: payload.id,
        branchingResponseId: payload.branchingQuestion?.id,
        surveyResponseId: payload.surveyResponseId
      });

      const response = await submitSurveyResponse(payload).unwrap();

      if (response?.data?.surveyResponseId) {
        // Store the global surveyResponseId for all future submissions
        if (!globalSurveyResponseId) {
          setGlobalSurveyResponseId(response.data.surveyResponseId);
          console.log('Global surveyResponseId set:', response.data.surveyResponseId);
        }
      }

      // Store individual response IDs for this question
      if (response?.data) {
        const newResponseId = response.data.id;
        const newBranchingResponseId = response.data.branchingQuestionResponseId;

        setAnswers(prev => ({
          ...prev,
          [question.id]: {
            ...prev[question.id],
            responseId: newResponseId || prev[question.id]?.responseId,
            branchingResponseId: newBranchingResponseId || prev[question.id]?.branchingResponseId,
          }
        }));

        console.log('Stored response IDs for question:', question.id, {
          responseId: newResponseId,
          branchingResponseId: newBranchingResponseId
        });
      }

      console.log('Answer submitted successfully for question:', question.id, 'Response ID:', response?.data?.id);
    } catch (error) {
      console.error('Failed to submit answer for question:', question.id, error);
      // You might want to show an error message to the user here
    }
  }, [answers, surveyId, submitSurveyResponse, globalSurveyResponseId]);

  // Handle final survey submission
  return {
    answers: Object.fromEntries(
      Object.entries(answers).map(([questionId, data]) => [questionId, data.answer])
    ),
    updateAnswer,
    isSubmitting,
    handleSubmitAnswer,
    globalSurveyResponseId, // Expose the global surveyResponseId
  };
};
