import { useState, useCallback } from 'react';
import { useSubmitSurveyResponseMutation } from '../../../apis/survaysAPI';
import { SurveyQuestionResponseDto, ISubmitSurveyResponsePayload, AttachmentDtoList } from '../../../apis/type';

interface SurveyAnswer {
  answers: string[];
  comment: string;
  attachmentDtoList?: AttachmentDtoList[];
  branchingQuestion?: {
    answers: string[];
    comment: string;
    attachmentDtoList?: AttachmentDtoList[];
  };
}

interface SurveyResponseState {
  [questionId: string]: {
    answer: SurveyAnswer;
    attachmentDtoList?: AttachmentDtoList[]
  };
}

export const useSurveyAnswerState = (surveyId: string) => {
  const [answers, setAnswers] = useState<SurveyResponseState>({});
  const [globalSurveyResponseId, setGlobalSurveyResponseId] = useState<string | undefined>(undefined);
  const [submitSurveyResponse, { isLoading: isSubmitting }] = useSubmitSurveyResponseMutation();

  // Update answer for a specific question
  const updateAnswer = useCallback((questionId: string, answer: SurveyAnswer) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: {
        ...prev[questionId],
        answer,
      }
    }));
  }, []);

  // Submit answer for a specific question
  const handleSubmitAnswer = useCallback(async (
    question: SurveyQuestionResponseDto,
    questionIndex: number
  ) => {
    const currentAnswer = answers[question.id]?.answer;

    if (!currentAnswer) {
      // No answer provided, skip submission
      return;
    }
    console.log('currentAnswer: ', currentAnswer);

    try {
      const payload: ISubmitSurveyResponsePayload = {
        surveyId,
        surveyQuestionId: question.id,
        answers: currentAnswer.answers || [],
        comment: currentAnswer.comment || '',
        attachmentDtoList: currentAnswer.attachmentDtoList || [],
        branchingQuestion: currentAnswer.branchingQuestion ? {
          id: question.branchingQuestion?.id || '',
          answers: currentAnswer.branchingQuestion.answers || [],
          comment: currentAnswer.branchingQuestion.comment || '',
          surveyQuestionId: question.branchingQuestion?.id || '',
          surveyResponseId: globalSurveyResponseId || '',
          attachmentDtoList: currentAnswer.branchingQuestion.attachmentDtoList || [],
        } : undefined as any,
        // Use global surveyResponseId for all submissions after the first one
        surveyResponseId: globalSurveyResponseId || undefined,
      };

      const response = await submitSurveyResponse(payload).unwrap();

      if (response?.data?.surveyResponseId) {
        // Store the global surveyResponseId for all future submissions
        if (!globalSurveyResponseId) {
          setGlobalSurveyResponseId(response.data.surveyResponseId);
          console.log('Global surveyResponseId set:', response.data.surveyResponseId);
        }
      }

      console.log('Answer submitted successfully for question:', question.id);
    } catch (error) {
      console.error('Failed to submit answer for question:', question.id, error);
      // You might want to show an error message to the user here
    }
  }, [answers, surveyId, submitSurveyResponse, globalSurveyResponseId]);

  // Handle final survey submission
  return {
    answers: Object.fromEntries(
      Object.entries(answers).map(([questionId, data]) => [questionId, data.answer])
    ),
    updateAnswer,
    isSubmitting,
    handleSubmitAnswer,
    globalSurveyResponseId, // Expose the global surveyResponseId
  };
};
