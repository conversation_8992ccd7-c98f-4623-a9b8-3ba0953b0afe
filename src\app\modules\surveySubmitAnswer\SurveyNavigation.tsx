import React from "react";
import { Button } from "react-bootstrap";
import { GrNext } from "react-icons/gr";
import { GrPrevious } from "react-icons/gr";

interface Props {
  currentQuestion: number;
  totalQuestions: number;
  isFirstQuestion: boolean;
  isLastQuestion: boolean;
  isSubmitting: boolean;
  isAttachmentUploading: boolean;
  onPrevious: () => void;
  onNext: () => void;
  onSubmit: () => void;
}

const SurveyNavigation: React.FC<Props> = ({
  currentQuestion,
  totalQuestions,
  isFirstQuestion,
  isLastQuestion,
  isSubmitting,
  isAttachmentUploading,
  onPrevious,
  onNext,
  onSubmit,
}) => {
  return (
    <div className="survey-navigation mt-4">
      <div className="d-flex justify-content-between align-items-center">
        {/* Back Button */}
        {isFirstQuestion ? (
          <div style={{ width: 65 }} />
        ) : (
          <Button
            variant="primary"
            onClick={onPrevious}
            disabled={isSubmitting || isAttachmentUploading}
            className="px-4"
            type="button"
            style={{borderRadius: "999px"}}
          >
            <GrPrevious size={20} />
          </Button>
        )}

        {/* Question Counter */}
        <div className="question-counter">
          <span className="fw-bold">
            ({currentQuestion}/{totalQuestions})
          </span>
        </div>

        {/* Next/Submit Button */}
        {isLastQuestion ? (
          <Button
            variant="primary"
            onClick={onSubmit}
            disabled={isSubmitting || isAttachmentUploading}
            className="px-4"
            type="button"
          >
            {isSubmitting
              ? "Submitting..."
              : isAttachmentUploading
              ? "Uploading..."
              : "Submit"}
          </Button>
        ) : (
          <Button
            variant="primary"
            onClick={onNext}
            disabled={isSubmitting || isAttachmentUploading}
            className="px-4"
            style={{borderRadius: "999px"}}
          >
            <GrNext size={20} />
          </Button>
        )}
      </div>
    </div>
  );
};

export default SurveyNavigation;
